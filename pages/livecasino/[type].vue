<template>
    <div class="wrapper-content__data">
        <div v-if="isLoading" class="wrapper-content__data__loading">
            <img :src="`${staticUrl}/loading.gif`" />
        </div>
        <div
            v-if="!isLoading"
            :class="{
                row: gameList?.length > 0,
            }"
        >
            <template v-if="gameList?.length">
                <div
                    v-for="(item, index) in gameList"
                    :key="`${item?.id}_${item?.table_id}`"
                    class="wrapper-content__data__col"
                >
                    <CommonLiveCasinoItem
                        :item="item"
                        :games="gameList"
                        :isLiveCasino="true"
                        :isLoadedVideo="!isIos || index <= 5"
                        :isFavorite="queryParams.sort === 'favorite'"
                        :jackpots="jackpots"
                        :previousJackpots="previousJackpots"
                        :viewers="viewers"
                        :previousViewers="previousViewers"
                    />
                </div>
            </template>
            <CommonGamesNoData v-else :typeData="typeData" :isGame="false" />
        </div>
        <div
            v-if="queryParams.page < totalPage && !isLoading"
            class="game-button"
        >
            <div
                v-if="isLoadMoreLoading"
                class="wrapper-content__data__loading"
            >
                <img :src="`${staticUrl}/loading.gif`" />
            </div>
            <CommonButton
                @click="emit('onLoadMoreGames')"
                :class="{ loading: isLoadMoreLoading }"
            >
                {{
                    `${$t('common.show_more')} (${
                        gameList?.length
                    }/${totalCasino})`
                }}
            </CommonButton>
        </div>
    </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useSocket } from '~/composables/use-socket'
import {
    PARTNER_GAME_ID,
    GAMES_DATA_CASINO,
    DEALER_LIVE,
} from '~/resources/live-casino'
import type { IGame, TokenInfo } from '~/interfaces/game'

const staticUrl = useRuntimeConfig().public.staticUrl

const { isIos } = useDevice()

const props = defineProps([
    'isLoading',
    'gameList',
    'queryParams',
    'typeData',
    'totalPage',
    'isLoadMoreLoading',
    'totalCasino',
])
const emit = defineEmits(['onLoadMoreGames'])
const socketStoreInstance = useSocket()
const {
    $verifyToken,
    $loadNanoPlayer,
    $clearPlayers,
    $showLivestreamThumbnails,
    $listenNetworkChange,
} = useNuxtApp()

const { previousJackpots, jackpots, viewers, previousViewers, isLostedInternet } =
    storeToRefs(socketStoreInstance)

const handleVisibilityChange = () => {
    if (document.visibilityState === 'visible' && window.innerWidth < 992) {
        loadVideo(props.gameList)
    }
}

const loadVideo = (gameList: any) => {
    $clearPlayers()
    const tokenMap = new Map<string, TokenInfo>(GAMES_DATA_CASINO)
    gameList.forEach((item: any) => {
        const liveCasinoKey = `${item.partner_provider}_${item.partner_game_id}`
        const tokenInfo = tokenMap.get(liveCasinoKey)
        // const domain = 'z01sv02.s2z.mooo.com'
        const domain = window.location.hostname
        if (tokenInfo && document.getElementById(liveCasinoKey)) {
            $verifyToken(liveCasinoKey, tokenInfo.id, tokenInfo.key, domain)
        }
    })
}

watch(
    () => props.isLoading,
    async (newValue) => {
        if (!newValue) {
            if (import.meta.client) {
                await nextTick(async () => {
                    setTimeout(() => {
                        loadVideo(props.gameList)
                    })
                });
            }
        }
    }
)

watch(
    () => isLostedInternet.value,
    (newVal) => {
        if (newVal) {
            $showLivestreamThumbnails()
        } else {
            loadVideo(props.gameList)
        }
    }
)

onMounted(async () => {
    await nextTick(async () => {
        if (import.meta.client) {
            setTimeout(() => {
                $loadNanoPlayer()
                $listenNetworkChange()
            }, 3000)
            setTimeout(() => {
                loadVideo(props.gameList)
            }, 4000)
        }
    })
    document.addEventListener('visibilitychange', handleVisibilityChange)
})

onBeforeUnmount(() => {
    document.removeEventListener('visibilitychange', handleVisibilityChange)
})
</script>

<style scoped></style>
