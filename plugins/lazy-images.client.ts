export default defineNuxtPlugin(() => {
    if (import.meta.client) {
        // Enhanced lazy loading with better performance
        const observerOptions = {
            root: null,
            rootMargin: '50px', // Load images 50px before they enter viewport
            threshold: 0.1
        }

        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    const img = entry.target as HTMLImageElement
                    
                    // Load the image
                    if (img.dataset.src) {
                        img.src = img.dataset.src
                        img.removeAttribute('data-src')
                    }
                    
                    // Load srcset if available
                    if (img.dataset.srcset) {
                        img.srcset = img.dataset.srcset
                        img.removeAttribute('data-srcset')
                    }
                    
                    // Add loading class for smooth transition
                    img.classList.add('loading')
                    
                    img.onload = () => {
                        img.classList.remove('loading')
                        img.classList.add('loaded')
                    }
                    
                    img.onerror = () => {
                        img.classList.remove('loading')
                        img.classList.add('error')
                    }
                    
                    // Stop observing this image
                    imageObserver.unobserve(img)
                }
            })
        }, observerOptions)

        // Observe all lazy images
        const observeLazyImages = () => {
            const lazyImages = document.querySelectorAll('img[data-src]')
            lazyImages.forEach((img) => {
                imageObserver.observe(img)
            })
        }

        // Initial observation
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', observeLazyImages)
        } else {
            observeLazyImages()
        }

        // Re-observe after navigation
        const router = useRouter()
        router.afterEach(() => {
            nextTick(() => {
                observeLazyImages()
            })
        })

        // Add CSS for smooth transitions
        const style = document.createElement('style')
        style.textContent = `
            img[data-src] {
                opacity: 0;
                transition: opacity 0.3s ease;
            }
            img.loading {
                opacity: 0.5;
            }
            img.loaded {
                opacity: 1;
            }
            img.error {
                opacity: 0.3;
                filter: grayscale(100%);
            }
        `
        document.head.appendChild(style)
    }
})
