export default defineNuxtPlugin(() => {
    // Optimize hero banner for LCP
    if (import.meta.client) {
        // Preconnect to static assets domain early
        const staticUrl = useRuntimeConfig().public.staticUrl
        if (staticUrl && staticUrl.startsWith('http')) {
            const link = document.createElement('link')
            link.rel = 'preconnect'
            link.href = new URL(staticUrl).origin
            link.crossOrigin = 'anonymous'
            document.head.appendChild(link)
        }

        // Optimize hero banner image loading
        const optimizeHeroBanner = () => {
            // Find hero banner images and ensure they have highest priority
            const heroBannerImages =
                document.querySelectorAll('.hero-banner img')
            heroBannerImages.forEach((img, index) => {
                if (index === 0) {
                    // First hero banner image gets highest priority
                    img.setAttribute('fetchpriority', 'high')
                    img.setAttribute('loading', 'eager')

                    // Ensure it's decoded immediately
                    if ('decode' in img) {
                        img.decode().catch(() => {
                            // Fallback if decode fails
                        })
                    }
                }
            })
        }

        // Run optimization when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', optimizeHeroBanner)
        } else {
            optimizeHeroBanner()
        }

        // Also run after navigation
        const router = useRouter()
        router.afterEach(() => {
            nextTick(() => {
                optimizeHeroBanner()
            })
        })
    }
})
