export default defineNuxtPlugin((nuxtApp) => {
    nuxtApp.vueApp.directive('nanoplayer', {
        mounted(el: HTMLElement) {
            // Add data attribute to mark element as needing nanoplayer
            el.setAttribute('data-nanoplayer', 'true')
            
            // Use intersection observer to load nanoplayer when element is visible
            const observer = new IntersectionObserver(
                (entries) => {
                    entries.forEach((entry) => {
                        if (entry.isIntersecting) {
                            observer.disconnect()
                            
                            // Load nanoplayer when element becomes visible
                            const { $loadNanoPlayer } = useNuxtApp()
                            $loadNanoPlayer().then((nanoPlayer) => {
                                if (nanoPlayer) {
                                    // Emit custom event when nanoplayer is ready
                                    el.dispatchEvent(new CustomEvent('nanoplayer-ready', {
                                        detail: { nanoPlayer }
                                    }))
                                }
                            }).catch((error) => {
                                console.error('Failed to load NanoPlayer:', error)
                                el.dispatchEvent(new CustomEvent('nanoplayer-error', {
                                    detail: { error }
                                }))
                            })
                        }
                    })
                },
                {
                    rootMargin: '100px', // Load 100px before element is visible
                    threshold: 0.1
                }
            )
            
            observer.observe(el)
            
            // Store observer for cleanup
            el._nanoplayerObserver = observer
        },
        
        unmounted(el: HTMLElement) {
            // Clean up observer
            if (el._nanoplayerObserver) {
                el._nanoplayerObserver.disconnect()
                delete el._nanoplayerObserver
            }
        }
    })
})
