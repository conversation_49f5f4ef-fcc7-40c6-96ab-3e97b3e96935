export default defineNuxtPlugin(() => {
    let loadPromise = null
    let isLoaded = false

    // Check if nanoplayer is needed on current page
    const isNanoPlayerNeeded = () => {
        if (typeof window === 'undefined') return false

        const route = useRoute()
        // Only load on specific pages that actually use nanoplayer
        return (
            route.path.includes('/live') ||
            route.path.includes('/casino') ||
            document.querySelector('[data-nanoplayer]') ||
            document.querySelector('.nanoplayer-container')
        )
    }

    return {
        provide: {
            loadNanoPlayer: () => {
                if (typeof window === 'undefined') {
                    return Promise.resolve(undefined)
                }

                // Return existing instance if already loaded
                if (window.NanoPlayer && isLoaded) {
                    return Promise.resolve(window.NanoPlayer)
                }

                // Return existing promise if loading
                if (loadPromise) {
                    return loadPromise
                }

                // Don't load if not needed
                if (!isNanoPlayerNeeded()) {
                    return Promise.resolve(undefined)
                }

                // Check if script already exists
                const existingScript = document.querySelector(
                    'script[src="/assets/js/nanoplayer.4.min.js"]'
                )

                if (existingScript && window.NanoPlayer) {
                    isLoaded = true
                    return Promise.resolve(window.NanoPlayer)
                }

                // Load script dynamically
                loadPromise = new Promise((resolve, reject) => {
                    const script = document.createElement('script')
                    script.src = '/assets/js/nanoplayer.4.min.js'
                    script.async = true
                    script.defer = true
                    script.crossOrigin = 'anonymous'
                    script.setAttribute('fetchpriority', 'low')

                    script.onload = () => {
                        isLoaded = true
                        // Wait a bit for NanoPlayer to initialize
                        setTimeout(() => {
                            resolve(window.NanoPlayer)
                        }, 50)
                    }

                    script.onerror = (error) => {
                        console.error('Failed to load NanoPlayer:', error)
                        reject(error)
                    }

                    document.head.appendChild(script)
                })

                return loadPromise
            },
        },
    }
})
