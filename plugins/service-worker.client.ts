export default defineNuxtPlugin(() => {
    if (import.meta.client && 'serviceWorker' in navigator) {
        // Advanced CSS optimization for render blocking
        const optimizeRenderBlockingCSS = () => {
            // 1. Defer non-critical CSS files
            const deferNonCriticalCSS = () => {
                const nonCriticalSelectors = [
                    'link[href*="swiper"]',
                    'link[href*="nuxt-icon"]',
                    'link[href*="button"]',
                    'link[href*="login-form"]'
                ]

                nonCriticalSelectors.forEach((selector) => {
                    const links = document.querySelectorAll(selector)
                    links.forEach((linkElement) => {
                        const link = linkElement as HTMLLinkElement
                        if (link.rel === 'stylesheet') {
                            // Convert to preload and load asynchronously
                            link.rel = 'preload'
                            link.as = 'style'
                            link.onload = function() {
                                this.onload = null
                                this.rel = 'stylesheet'
                            }
                        }
                    })
                })
            }

            // 2. Lazy load below-fold content
            const lazyLoadBelowFoldCSS = () => {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach((entry) => {
                        if (entry.isIntersecting) {
                            // Show swiper and other below-fold elements
                            const swiperContainers = document.querySelectorAll('.swiper-container')
                            swiperContainers.forEach((containerElement) => {
                                const container = containerElement as HTMLElement
                                container.style.opacity = '1'
                            })

                            // Show footer
                            const footerElement = document.querySelector('.footer')
                            if (footerElement) {
                                const footer = footerElement as HTMLElement
                                footer.style.display = 'block'
                            }

                            observer.disconnect()
                        }
                    })
                })

                // Observe the first below-fold element
                const belowFoldElement = document.querySelector('.swiper-container, .footer, .game-section')
                if (belowFoldElement) {
                    observer.observe(belowFoldElement)
                }
            }

            // Execute optimizations
            deferNonCriticalCSS()
            setTimeout(lazyLoadBelowFoldCSS, 100)
        }

        // Register service worker
        const registerSW = async () => {
            try {
                const registration = await navigator.serviceWorker.register(
                    '/sw.js',
                    {
                        scope: '/',
                    }
                )

                console.log(
                    '[SW] Service Worker registered successfully:',
                    registration
                )

                // Handle when service worker is ready
                if (registration.active) {
                    console.log('[SW] Service Worker is active')
                }

                // Handle when new service worker is activated
                navigator.serviceWorker.addEventListener(
                    'controllerchange',
                    () => {
                        console.log('[SW] New service worker activated')
                        // Reload page to use new service worker
                        // window.location.reload()
                    }
                )

                // Handle message from service worker
                navigator.serviceWorker.addEventListener('message', (event) => {
                    console.log('[SW] Message from service worker:', event.data)
                })
            } catch (error) {
                console.error('[SW] Service Worker registration failed:', error)
            }
        }

        // Optimize render blocking CSS immediately
        optimizeRenderBlockingCSS()

        // Register service worker when page is loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', registerSW)
        } else {
            registerSW()
        }
    }
})
