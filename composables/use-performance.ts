export const usePerformance = () => {
    const metrics = ref({
        lcp: 0,
        fid: 0,
        cls: 0,
        fcp: 0,
        ttfb: 0,
        images: {
            total: 0,
            optimized: 0,
            formats: { avif: 0, webp: 0, jpg: 0, png: 0 }
        },
        lighthouse: {
            performance: 0,
            accessibility: 0,
            bestPractices: 0,
            seo: 0
        }
    })

    const measureWebVitals = () => {
        if (typeof window === 'undefined') return

        // Measure LCP (Largest Contentful Paint)
        const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            const lastEntry = entries[entries.length - 1]
            metrics.value.lcp = Math.round(lastEntry.startTime)
            console.log('🎯 LCP:', metrics.value.lcp + 'ms')
        })
        lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true })

        // Measure FID (First Input Delay)
        const fidObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            entries.forEach((entry) => {
                metrics.value.fid = Math.round(entry.processingStart - entry.startTime)
                console.log('⚡ FID:', metrics.value.fid + 'ms')
            })
        })
        fidObserver.observe({ type: 'first-input', buffered: true })

        // Measure CLS (Cumulative Layout Shift)
        let clsValue = 0
        const clsObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            entries.forEach((entry) => {
                if (!entry.hadRecentInput) {
                    clsValue += entry.value
                }
            })
            metrics.value.cls = Math.round(clsValue * 1000) / 1000
            console.log('📐 CLS:', metrics.value.cls)
        })
        clsObserver.observe({ type: 'layout-shift', buffered: true })

        // Measure FCP (First Contentful Paint)
        const fcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            entries.forEach((entry) => {
                if (entry.name === 'first-contentful-paint') {
                    metrics.value.fcp = Math.round(entry.startTime)
                    console.log('🎨 FCP:', metrics.value.fcp + 'ms')
                }
            })
        })
        fcpObserver.observe({ type: 'paint', buffered: true })

        // Measure TTFB (Time to First Byte)
        const navigationEntries = performance.getEntriesByType('navigation')
        if (navigationEntries.length > 0) {
            const navEntry = navigationEntries[0] as PerformanceNavigationTiming
            metrics.value.ttfb = Math.round(navEntry.responseStart - navEntry.requestStart)
            console.log('🌐 TTFB:', metrics.value.ttfb + 'ms')
        }
    }

    const analyzeImages = () => {
        if (typeof window === 'undefined') return

        const images = document.querySelectorAll('img')
        metrics.value.images.total = images.length

        images.forEach((img) => {
            const src = img.src || img.dataset.src || ''
            
            if (src.includes('.avif')) {
                metrics.value.images.formats.avif++
                metrics.value.images.optimized++
            } else if (src.includes('.webp')) {
                metrics.value.images.formats.webp++
                metrics.value.images.optimized++
            } else if (src.includes('.jpg') || src.includes('.jpeg')) {
                metrics.value.images.formats.jpg++
            } else if (src.includes('.png')) {
                metrics.value.images.formats.png++
            }
        })

        console.log('🖼️ Image Analysis:', {
            total: metrics.value.images.total,
            optimized: metrics.value.images.optimized,
            optimizationRate: Math.round((metrics.value.images.optimized / metrics.value.images.total) * 100) + '%'
        })
    }

    const generateLighthouseReport = () => {
        const report = {
            performance: calculatePerformanceScore(),
            recommendations: generateRecommendations(),
            metrics: metrics.value
        }
        
        console.log('🏆 Performance Report:', report)
        return report
    }

    const calculatePerformanceScore = () => {
        let score = 100

        // LCP scoring (0-2.5s = good, 2.5-4s = needs improvement, >4s = poor)
        if (metrics.value.lcp > 4000) score -= 30
        else if (metrics.value.lcp > 2500) score -= 15

        // FID scoring (0-100ms = good, 100-300ms = needs improvement, >300ms = poor)
        if (metrics.value.fid > 300) score -= 25
        else if (metrics.value.fid > 100) score -= 10

        // CLS scoring (0-0.1 = good, 0.1-0.25 = needs improvement, >0.25 = poor)
        if (metrics.value.cls > 0.25) score -= 25
        else if (metrics.value.cls > 0.1) score -= 10

        // FCP scoring (0-1.8s = good, 1.8-3s = needs improvement, >3s = poor)
        if (metrics.value.fcp > 3000) score -= 20
        else if (metrics.value.fcp > 1800) score -= 10

        return Math.max(0, score)
    }

    const generateRecommendations = () => {
        const recommendations = []

        if (metrics.value.lcp > 2500) {
            recommendations.push('Optimize Largest Contentful Paint: Consider using AVIF images, preloading critical resources')
        }

        if (metrics.value.fid > 100) {
            recommendations.push('Reduce First Input Delay: Defer non-critical JavaScript, use code splitting')
        }

        if (metrics.value.cls > 0.1) {
            recommendations.push('Minimize Cumulative Layout Shift: Add width/height to images, reserve space for dynamic content')
        }

        if (metrics.value.images.optimized / metrics.value.images.total < 0.8) {
            recommendations.push('Optimize more images: Convert to AVIF/WebP formats, use responsive images')
        }

        return recommendations
    }

    return {
        metrics: readonly(metrics),
        measureWebVitals,
        analyzeImages,
        generateLighthouseReport
    }
}
