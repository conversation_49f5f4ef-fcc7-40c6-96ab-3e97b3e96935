export const useNanoPlayer = () => {
    const { $loadNanoPlayer } = useNuxtApp()
    
    // Lazy load nanoplayer only when component is actually visible
    const loadNanoPlayerOnDemand = async () => {
        try {
            const nanoPlayer = await $loadNanoPlayer()
            return nanoPlayer
        } catch (error) {
            console.error('Failed to load NanoPlayer:', error)
            return null
        }
    }

    // Load nanoplayer when element becomes visible (intersection observer)
    const loadNanoPlayerOnVisible = (element: HTMLElement) => {
        if (!element) return Promise.resolve(null)

        return new Promise((resolve) => {
            const observer = new IntersectionObserver(
                (entries) => {
                    entries.forEach((entry) => {
                        if (entry.isIntersecting) {
                            observer.disconnect()
                            loadNanoPlayerOnDemand().then(resolve)
                        }
                    })
                },
                {
                    rootMargin: '50px', // Load 50px before element is visible
                    threshold: 0.1
                }
            )
            
            observer.observe(element)
        })
    }

    // Check if nanoplayer is already loaded
    const isNanoPlayerLoaded = () => {
        return typeof window !== 'undefined' && !!window.NanoPlayer
    }

    return {
        loadNanoPlayerOnDemand,
        loadNanoPlayerOnVisible,
        isNanoPlayerLoaded
    }
}
