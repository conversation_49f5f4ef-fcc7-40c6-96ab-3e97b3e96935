const imageInput =
    process.argv?.length && process.argv?.length > 2
        ? `/${process.argv[process.argv.length - 1]}`
        : ''

const sharp = require('sharp')
const fs = require('fs-extra')
const path = require('path')
const crypto = require('crypto')

// Enhanced configuration for different image types
const IMAGE_CONFIGS = {
    hero: {
        avif: { quality: 80, effort: 6 }, // Reduced quality for better compression
        webp: { quality: 80, effort: 6 },
        fallback: { quality: 85 }
    },
    thumbnail: {
        avif: { quality: 70, effort: 4 }, // More aggressive compression
        webp: { quality: 70, effort: 4 },
        fallback: { quality: 75 }
    },
    icon: {
        avif: { quality: 85, effort: 6 }, // Slightly reduced for icons
        webp: { quality: 85, effort: 6 },
        fallback: { quality: 90 }
    },
    background: {
        avif: { quality: 65, effort: 4 }, // Very aggressive for backgrounds
        webp: { quality: 65, effort: 4 },
        fallback: { quality: 70 }
    },
    default: {
        avif: { quality: 75, effort: 4 }, // Reduced default quality
        webp: { quality: 75, effort: 4 },
        fallback: { quality: 80 }
    }
}

// Directory where your images are located
const inputDir = `./assets/images${imageInput}`
// Base directory where converted images will be saved
const outputDir = `./public/assets/images${imageInput}`
// Metadata file to track converted images
const metadataFile = path.join(outputDir, 'metadata.json')

// Ensure the output directory exists
fs.ensureDirSync(outputDir)

// Load existing metadata
let metadata = {}
if (fs.existsSync(metadataFile)) {
    metadata = JSON.parse(fs.readFileSync(metadataFile, 'utf8'))
}

// Function to generate a hash of a file
const getFileHash = async (filePath) => {
    const fileBuffer = await fs.readFile(filePath)
    return crypto.createHash('md5').update(fileBuffer).digest('hex')
}

// Function to check if conversion is needed
const isConversionNeeded = async (inputFilePath, outputFilePath) => {
    const relativePath = path.relative(inputDir, inputFilePath) // Get relative path for metadata
    const stats = await fs.stat(inputFilePath)
    const fileHash = await getFileHash(inputFilePath)

    if (
        metadata[relativePath] &&
        metadata[relativePath].hash === fileHash &&
        metadata[relativePath].size === stats.size
    ) {
        return false // No conversion needed
    }

    // Update metadata
    metadata[relativePath] = {
        hash: fileHash,
        size: stats.size,
        lastModified: stats.mtimeMs,
    }

    return true
}

// Function to save metadata to file
const saveMetadata = () => {
    fs.writeFileSync(metadataFile, JSON.stringify(metadata, null, 2), 'utf8')
}

// Function to convert and compress image to AVIF and WebP
const convertAndCompressImage = async (inputFilePath, outputFilePath) => {
    if (!path.extname(inputFilePath)?.trim()) {
        return
    }
    if (!path.extname(inputFilePath)?.match(/\.(jpg|jpeg|png|gif|svg)$/i)) {
        return
    }

    const needsConversion = await isConversionNeeded(inputFilePath, outputFilePath)
    if (!needsConversion) {
        console.log(`Skipped conversion for ${inputFilePath}, no changes detected.`)
        return
    }

    console.log(`Converted and compressed ${inputFilePath} to AVIF and WebP.`)
    const fileName = path.basename(inputFilePath, path.extname(inputFilePath))

    // Convert to AVIF with compression
    await sharp(inputFilePath)
        .avif({ quality: 50 }) // Adjust quality as needed
        .toFile(path.join(outputFilePath, `${fileName}.avif`))

    // Convert to WebP with compression
    await sharp(inputFilePath)
        .webp({ quality: 50 }) // Adjust quality as needed
        .toFile(path.join(outputFilePath, `${fileName}.webp`))

    // Copy original file
    await fs.copyFile(
        inputFilePath,
        `${outputFilePath}/${fileName}${path.extname(inputFilePath)}`
    )
}

// Function to process directory recursively
const processDirectory = async (dir, baseOutputDir) => {
    const items = await fs.readdir(dir)

    for (const item of items) {
        const inputPath = path.join(dir, item)
        const stats = await fs.stat(inputPath)

        if (stats.isDirectory()) {
            const newBaseOutputDir = path.join(baseOutputDir, item)
            await fs.ensureDir(newBaseOutputDir)
            await processDirectory(inputPath, newBaseOutputDir)
        } else if (stats.isFile()) {
            await convertAndCompressImage(inputPath, baseOutputDir)
        }
    }
}

// Run the conversion and compression process
processDirectory(inputDir, outputDir)
    .then(() => {
        saveMetadata()
        console.log('All images converted and compressed.')
    })
    .catch((error) =>
        console.error('Error during conversion and compression:', error)
    )
