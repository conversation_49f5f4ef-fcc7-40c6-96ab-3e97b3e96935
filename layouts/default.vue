<template>
    <div
        class="wrapper flex min-h-screen flex-col pt-12 lg:pt-0"
        :style="{ minHeight: `${innerHeight}px` }"
    >
        <LayoutHeader v-if="isHideHeader" />
        <main class="flex-1">
            <slot />
        </main>
        <LazyLayoutFooter v-if="!isHideFooter" />
        <ClientOnly>
            <LazyModalGo88 :show="showGo88Modal" />
            <LayoutBottomBar v-if="!isDeskTop && isHideHeader" />
            <template v-if="!disableUserModal">
                <LazyModalLogin
                    :show="
                        showLoginModal &&
                        !currentModal &&
                        !showFifaClubBannerModal
                    "
                />
                <LazyModalRegisterSuccess :show="showRegisterSuccessModal" />
                <LazyModalRegister
                    :show="
                        showRegisterModal &&
                        !currentModal &&
                        !showFifaClubBannerModal
                    "
                />
            </template>
            <LazyModalForgotPassword :show="showForgotPasswordModal" />
            <!--            <LazyModalRenewPasswordTelegram-->
            <!--                :show="showRenewPasswordTelegramModal"-->
            <!--            />-->
            <LazyModalConfirmLogout :show="showModalConfirmLogout" />
            <LazyModalBanksSupport :show="showModalBankSupport" />
            <!-- <LazyModalInsuranceSportAmount
                :show="currentModal === PROMOTION_POPUP_METHOD.INSURANCE_AMOUNT"
            /> -->
            <LazyModalLoginFail :show="showLoginFailModal" />
            <!-- <LazyModalInsuranceSportNotify
                :show="currentModal === PROMOTION_POPUP_METHOD.INSURANCE_NOTIFY"
            /> -->
            <LazyModalSvip
                :show="currentModal === PROMOTION_POPUP_METHOD.SVIP"
            />
            <LazyModalRefundCasino
                :show="
                    currentModal === PROMOTION_POPUP_METHOD.LIVE_CASINO_REFUND
                "
            />
            <!-- <LazyModalRaceTop
                :show="currentModal === PROMOTION_POPUP_METHOD.RACE_TOP"
            /> -->
            <!-- <LazyModalGoldenStarWarrios
                :show="
                    currentModal === PROMOTION_POPUP_METHOD.GOLDEN_STAR_WARRIORS
                "
            /> -->
            <!-- <LazyModalClubWorldCupTop
                :show="
                    currentModal === PROMOTION_POPUP_METHOD.CLUB_WORLD_CUP_TOP
                " -->
            <!-- /> -->
            <!-- <LazyModalClubWorldCup
                :show="
                    currentModal === PROMOTION_POPUP_METHOD.CLUB_WORLD_CUP &&
                    !showFifaClubBannerModal &&
                    !showUefaChampionBannerModal &&
                    !showFifaClubModal
                "
            /> -->
            <LazyCommonLivechat />
            <LazyCommonTelegram />
            <LazyModalUpdateFullname v-show="showUpdateFullNameModal" />
            <!-- <LazyModalUefaChampionLeague :show="showUefaChampionModal" /> -->
            <!-- <LazyModalFifaClub :show="showFifaClubModal" /> -->
            <!-- <LazyModalUefaChampionLeagueBanner
                :show="showUefaChampionBannerModal"
            /> -->
            <!-- <LazyModalFifaClubBanner :show="showFifaClubBannerModal" />
            <LazyModalUefaPopup
                :show="currentModal === UEFA_CHAMPION_LEAGUE_METHOD"
            /> -->
            <!-- <LazyModalFifaClubPopup
                :show="
                    currentModal === FIFA_CLUB_METHOD &&
                    !showFifaClubBannerModal
                "
            /> -->
            <LazyModalDanhDeMienPhiCashbackAward
                :show="
                    [
                        PROMOTION_POPUP_METHOD.LUCKY_DRAW_1,
                        PROMOTION_POPUP_METHOD.LUCKY_DRAW_2,
                    ].includes(currentModal)
                "
            />
            <!-- <LazyModalDanhDeMienPhiCashback
                :luckyDraw="luckyDraw"
                :show="
                    showLuckyDrawCashbackModal &&
                    !currentModal &&
                    !showBetFreeModal
                "
            /> -->
            <template v-if="isAfterEndDate">
                <LazyModalChoTet
                    :show="
                        currentModal === PROMOTION_POPUP_METHOD.FREE_SPIN_TET
                    "
                />
            </template>
            <template v-else>
                <LazyModalFreeSpin
                    :show="currentModal === PROMOTION_POPUP_METHOD.FREE_SPIN"
                />
            </template>
            <!-- <LazyModalBankWarning v-if="user?.tp_token"/> -->
        </ClientOnly>
        <ClientOnly v-if="isHideHeader">
            <MiniGame
                v-if="isShowMiniGame"
                @closeMiniGame="isShowMiniGame = false"
            />
            <FloatingLucky />
        </ClientOnly>
        <!-- <CommonPagetop /> -->
        <ClientOnly>
            <HomeFloatingGo88 />
        </ClientOnly>
    </div>
</template>
<script lang="ts" setup>
import { usePromotion } from '~/composables/use-promotion'
import { useModalStore } from '~/stores'
import { MODAL_NAME } from '~/constants/common'
import {
    FOOTER_HIDDEN_MOBILE_ROUTES,
    PROMOTION_POPUP_METHOD,
} from '~/constants/common'
import { useUserStore } from '~/composables/use-user'
import { DISABLE_USER_MODAL } from '~/resources/header'
import { onMounted } from 'vue'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import { useCountdown } from '~/composables/common/use-countdown'
import utc from 'dayjs/plugin/utc'
// import { useEventDanhDeStore } from '~/composables/use-event-danh-de'

dayjs.extend(utc)
dayjs.extend(timezone)
const UEFA_CHAMPION_LEAGUE_METHOD =
    useRuntimeConfig().public.UEFA_CHAMPION_LEAGUE_METHOD
const FIFA_CLUB_METHOD = useRuntimeConfig().public.FIFA_CLUB_METHOD
const { innerHeight, isDeskTop } = useWindowSize()
const route = useRoute()
const useModalStoreInstance = useModalStore()
const {
    showLoginModal,
    showRegisterModal,
    showRegisterSuccessModal,
    showForgotPasswordModal,
    // showRenewPasswordTelegramModal,
    showModalConfirmLogout,
    showModalBankSupport,
    currentModal,
    showLoginFailModal,
    showUpdateFullNameModal,
    showGo88Modal,
    showUefaChampionModal,
    // noShowAgainUefaChampionModal,
    showUefaChampionBannerModal,
    // noShowAgainUefaChampionBannerModal,
    showFifaClubModal,
    showFifaClubBannerModal,
    // noShowAgainFifaClubModal,
    noShowAgainFifaClubBannerModal,
    // showLuckyDrawCashbackModal,
    // showBetFreeModal,
    // showBetFreeModalMB,
} = storeToRefs(useModalStoreInstance)
const { queueModal } = useModalStoreInstance
const useUserStoreInstance = useUserStore()
const { loginToken } = useUserStoreInstance
const { user, isLogged } = storeToRefs(useUserStoreInstance)
const { refreshInterval } = useUserStore()
// const useEventDanhDeStoreInstance = useEventDanhDeStore()
// const { getLuckyDrawStatus } = useEventDanhDeStoreInstance
// const { luckyDraw } = storeToRefs(useEventDanhDeStoreInstance)
const { getPromotionPopup } = usePromotion()
const {
    FREE_SPIN_EVENT_START_DAY,
    FREE_SPIN_EVENT_END_DAY,
    GOLDEN_STAR_WARRIORS_EVENT_END_DAY,
    CLUB_WORLD_CUP_EVENT_END_DAY,
} = useRuntimeConfig().public
const { isAfterEndDate } = useCountdown(
    FREE_SPIN_EVENT_START_DAY,
    FREE_SPIN_EVENT_END_DAY
)
const { isBetweenTimeEvent: isFifaClubEventActive, getServerTime } =
    useCountdown(
        dayjs
            .tz(
                useRuntimeConfig().public.FIFA_CLUB_EVENT_START_DAY,
                'Asia/Ho_Chi_Minh'
            )
            .format(),
        dayjs
            .tz(
                useRuntimeConfig().public.FIFA_CLUB_EVENT_END_DAY,
                'Asia/Ho_Chi_Minh'
            )
            .format()
    )
const showAgainHoursFifaClub = Number(
    useRuntimeConfig().public.FIFA_CLUB_MODAL_SHOW_AGAIN_HOURS
)
const showAgainMillisecondsFifaClub = showAgainHoursFifaClub * 60 * 60 * 1000
const isShowMiniGame = ref(true)
const headerHiddenRoutes = ['maintain']

const isHideFooter = computed(() => {
    const routeName =
        route.name === 'slug'
            ? route.path?.substring(1)
            : route.name?.toString() || ''
    return (
        !isDeskTop.value &&
        FOOTER_HIDDEN_MOBILE_ROUTES.some(
            (route) => routeName === route || routeName.startsWith(`${route}-`)
        )
    )
})

// const showAgainHours = Number(
//     useRuntimeConfig().public.UEFA_MODAL_SHOW_AGAIN_HOURS
// )

const disableUserModal = computed(() => {
    return DISABLE_USER_MODAL.some((el) => el === route.name)
})

const isHideHeader = computed(() => {
    const _routeName = route.name?.toString() || ''
    return !headerHiddenRoutes.includes(_routeName)
})

// const canShowCashback = computed(() => {
//     return showLuckyDrawCashbackModal && !currentModal
// })

// const checkShowLuckyDrawCashbackModal = () => {
//     if (
//         !currentModal.value &&
//         !showBetFreeModalMB.value &&
//         [
//             luckyDraw?.value?.lucky_number_1,
//             luckyDraw?.value?.lucky_number_2,
//         ].some((num) => num && typeof num !== 'string')
//     ) {
//         showLuckyDrawCashbackModal.value = true
//     }
// }

onMounted(async () => {
    useModalStoreInstance.initShowAgainUefaChampionBannerModal()
    useModalStoreInstance.initShowAgainFifaClubBannerModal()
    useModalStoreInstance.initShowAgainFifaClubModal()
    await nextTick(async () => {
        const now = dayjs().tz('Asia/Ho_Chi_Minh')
        const end = dayjs(GOLDEN_STAR_WARRIORS_EVENT_END_DAY).tz(
            'Asia/Ho_Chi_Minh'
        )
        const endWorldCup = dayjs(CLUB_WORLD_CUP_EVENT_END_DAY).tz(
            'Asia/Ho_Chi_Minh'
        )
        const isBeforeEndEvent = now.isBefore(end, 'second')
        const isBeforeEndEventWorldCup = now.isBefore(endWorldCup, 'second')
        if (route?.path === '/' && route?.query?.token) {
            await loginToken({ token: route?.query?.token })
        }
        if (user.value) {
            Promise.all([refreshInterval(), getPromotionPopup()])
        }
        if (isBeforeEndEvent) {
            const popupClosed = process.client
                ? localStorage.getItem('goldenStarWarriosPopupClosed') ===
                  'true'
                : false

            if (!popupClosed) {
                queueModal(PROMOTION_POPUP_METHOD.GOLDEN_STAR_WARRIORS)
            }
        }
        if (isBeforeEndEventWorldCup) {
            const popupClosed = import.meta.client
                ? localStorage.getItem('clubWorldCupPopupClosed') === 'true'
                : false

            if (!popupClosed) {
                queueModal(PROMOTION_POPUP_METHOD.CLUB_WORLD_CUP)
            }
        }
        // if (isLogged.value) {
        //     await getLuckyDrawStatus()
        // }
        // checkShowLuckyDrawCashbackModal()
        if (!isFifaClubEventActive.value) {
            showFifaClubModal.value = false
            showFifaClubBannerModal.value = false
            return
        }

        // if (
        //     noShowAgainUefaChampionModal.value &&
        //     useModalStoreInstance.uefaChampionModalCheckTime
        // ) {
        //     const checkTime = dayjs(
        //         useModalStoreInstance.uefaChampionModalCheckTime
        //     ).tz('Asia/Ho_Chi_Minh')
        //     const now = getServerTime()
        //     const timePassed = now.diff(checkTime)
        //
        //     if (timePassed >= showAgainMilliseconds) {
        //         useModalStoreInstance.setShowAgainUefaChampionModal(false)
        //         showUefaChampionModal.value = true
        //     } else {
        //         const remainingTime = showAgainMilliseconds - timePassed
        //         setTimeout(() => {
        //             useModalStoreInstance.setShowAgainUefaChampionModal(false)
        //             showUefaChampionModal.value = true
        //         }, remainingTime)
        //     }
        // } else {
        //     showUefaChampionModal.value = true
        // }

        if (
            noShowAgainFifaClubBannerModal.value &&
            useModalStoreInstance.fifaClubBannerModalCheckTime
        ) {
            const checkTime = dayjs(
                useModalStoreInstance.fifaClubBannerModalCheckTime
            ).tz('Asia/Ho_Chi_Minh')
            const now = getServerTime()
            const timePassed = now.diff(checkTime)

            if (timePassed >= showAgainMillisecondsFifaClub) {
                useModalStoreInstance.setShowAgainFifaClubBannerModal(false)
                showFifaClubBannerModal.value = true
            } else {
                const remainingTime = showAgainMillisecondsFifaClub - timePassed
                setTimeout(() => {
                    useModalStoreInstance.setShowAgainFifaClubBannerModal(false)
                    showFifaClubBannerModal.value = true
                }, remainingTime)
            }
        } else if (!noShowAgainFifaClubBannerModal.value) {
            showFifaClubBannerModal.value = true
        }
    })

    if (typeof window !== 'undefined') {
        window.addEventListener('storage', function (event) {
            if (event.key === 'reload') {
                const _reload = JSON.parse(localStorage.getItem('reload') || '')
                if (_reload) {
                    reloadNuxtApp()
                    this.localStorage.removeItem('reload')
                }
            }
        })
    }
})

watch(
    () => route.query,
    async (query) => {
        await nextTick(async () => {
            if (import.meta.client) {
                // if (isLogged.value) {
                //     await getLuckyDrawStatus()
                // }
                // checkShowLuckyDrawCashbackModal()
                if (!query?.modal || user.value) {
                    return
                }
                const _modal = Array.isArray(query?.modal)
                    ? query?.modal[0]
                    : query?.modal
                const modalName = _modal?.split('?')[0]
                if (modalName === MODAL_NAME.LOGIN) {
                    showLoginModal.value = true
                    return
                }
                if (modalName === MODAL_NAME.REGISTER) {
                    showRegisterModal.value = true
                    return
                }
            }
        })
    },
    { immediate: true }
)

watch(
    () => isFifaClubEventActive.value,
    (newValue) => {
        if (!newValue) {
            showFifaClubModal.value = false
            showFifaClubBannerModal.value = false
            noShowAgainFifaClubBannerModal.value = true
        }
    }
)

// watch(currentModal, () => {
//     checkShowLuckyDrawCashbackModal()
// })

// watch(
//     () => showBetFreeModal.value,
//     (value) => {
//         if (!value) {
//             showLuckyDrawCashbackModal.value = false
//         }
//     }
// )

watch(
    () => isLogged.value,
    async (value) => {
        if (value) {
            refreshInterval()
            // await getLuckyDrawStatus()
            // checkShowLuckyDrawCashbackModal()
        }
    }
)

// watch(
//     () => showUefaChampionBannerModal.value,
//     (newValue) => {
//         if (!newValue && !useModalStoreInstance.uefaChampionModalCheckTime) {
//             showUefaChampionModal.value = true
//         } else {
//             showUefaChampionModal.value = false
//         }
//     }
// )
</script>
