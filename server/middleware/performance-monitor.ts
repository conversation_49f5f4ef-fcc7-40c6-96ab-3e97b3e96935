import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, getHeader, setHeader } from 'h3'

export default defineEventHandler(async (event) => {
    const startTime = Date.now()
    const url = event.node.req.url
    
    // Skip monitoring for assets and API calls
    if (!url || url.startsWith('/api') || url.startsWith('/assets') || url.startsWith('/_nuxt')) {
        return
    }

    // Add Server-Timing header for performance monitoring
    event.node.res.on('finish', () => {
        const endTime = Date.now()
        const duration = endTime - startTime
        
        // Set Server-Timing header
        const serverTiming = [
            `total;dur=${duration}`,
            `server;desc="Server Processing Time"`
        ].join(', ')
        
        setHeader(event, 'Server-Timing', serverTiming)
        
        // Log performance metrics in development
        if (process.env.NODE_ENV === 'development') {
            console.log(`🚀 ${event.node.req.method} ${url} - ${duration}ms`)
            
            // Log slow requests
            if (duration > 1000) {
                console.warn(`⚠️ Slow request: ${url} took ${duration}ms`)
            }
        }
    })

    // Add performance hints headers
    setHeader(event, 'X-DNS-Prefetch-Control', 'on')
    setHeader(event, 'X-Frame-Options', 'DENY')
    setHeader(event, 'X-Content-Type-Options', 'nosniff')
    
    // Add resource hints for critical assets
    if (url === '/') {
        const { staticUrl } = useRuntimeConfig().public
        setHeader(event, 'Link', [
            `<${staticUrl}/home/<USER>/jackpot/pc.avif>; rel=preload; as=image; type=image/avif`,
            `<${staticUrl}/home/<USER>/jackpot/mb.avif>; rel=preload; as=image; type=image/avif; media="(max-width: 991px)"`,
            '</assets/css/critical.css>; rel=preload; as=style'
        ].join(', '))
    }
})
