import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, getHeader, setHeader } from 'h3'

export default defineEventHandler(async (event) => {
    const url = event.node.req.url

    // Only handle image requests
    if (!url || !url.match(/\.(jpg|jpeg|png|webp|avif)$/i)) {
        return
    }

    // Skip if already optimized
    if (url.includes('/optimized/')) {
        return
    }

    const acceptHeader = getHeader(event, 'accept') || ''
    const userAgent = getHeader(event, 'user-agent') || ''

    // Determine best format based on browser support
    let preferredFormat = 'jpg'
    if (acceptHeader.includes('image/avif')) {
        preferredFormat = 'avif'
    } else if (acceptHeader.includes('image/webp')) {
        preferredFormat = 'webp'
    }

    // Determine device type for responsive images
    const isMobile = /Mobile|Android|iPhone|iPad/i.test(userAgent)
    const screenWidth = isMobile ? 640 : 1920

    // Try to serve optimized version
    const originalPath = url.replace(/^\//, '')
    const pathParts = originalPath.split('.')
    const basePath = pathParts.slice(0, -1).join('.')

    // Construct optimized image path
    const optimizedPath = `/assets/images/optimized/${basePath}_${screenWidth}w.${preferredFormat}`

    try {
        // Check if optimized version exists
        const fs = await import('fs')
        const path = await import('path')
        const publicPath = path.join(process.cwd(), 'public', optimizedPath)

        if (fs.existsSync(publicPath)) {
            // Set appropriate headers for optimized images
            setHeader(
                event,
                'Cache-Control',
                'public, max-age=31536000, immutable'
            )
            setHeader(event, 'Vary', 'Accept, User-Agent')
            setHeader(event, 'Content-Type', `image/${preferredFormat}`)

            // Redirect to optimized version
            event.node.res.writeHead(302, {
                Location: optimizedPath,
            })
            event.node.res.end()
            return
        }
    } catch (error) {
        // Fall back to original image
        console.warn('Failed to serve optimized image:', error)
    }

    // Set cache headers for original images
    setHeader(event, 'Cache-Control', 'public, max-age=86400') // 1 day
    setHeader(event, 'Vary', 'Accept, User-Agent')
})
