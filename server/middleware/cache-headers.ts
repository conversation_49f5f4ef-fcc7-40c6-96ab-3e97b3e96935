export default defineEventHandler((event) => {
    const url = getRequestURL(event)
    const pathname = url.pathname

    // Skip if not a static asset request
    if (!pathname.startsWith('/assets/') && !pathname.startsWith('/_nuxt/')) {
        return
    }

    // Cache configuration for different asset types
    const cacheConfigs = [
        // Hero banner images - cache for 1 year (immutable) with LCP optimization
        {
            pattern: /^\/assets\/images\/home\/<USER>\/.+\.(jpg|jpeg|png|webp|avif)$/,
            maxAge: 31536000, // 1 year
            immutable: true,
            description: 'Hero banner images',
            extraHeaders: {
                'X-Content-Type-Options': 'nosniff',
                'Priority': 'u=2, i', // High priority for LCP images
            }
        },
        
        // Other critical images - cache for 6 months
        {
            pattern: /^\/assets\/images\/.+\.(jpg|jpeg|png|webp|avif)$/,
            maxAge: 15552000, // 6 months
            immutable: false,
            description: 'General images'
        },
        
        // JavaScript files - cache for 1 year (immutable due to hash)
        {
            pattern: /^\/assets\/js\/.+\.js$/,
            maxAge: 31536000, // 1 year
            immutable: true,
            description: 'JavaScript files'
        },
        
        // Nuxt generated assets - cache for 1 year (immutable due to hash)
        {
            pattern: /^\/_nuxt\/.+\.(js|css)$/,
            maxAge: 31536000, // 1 year
            immutable: true,
            description: 'Nuxt generated assets'
        },
        
        // CSS files - cache for 1 year
        {
            pattern: /^\/assets\/css\/.+\.css$/,
            maxAge: 31536000, // 1 year
            immutable: true,
            description: 'CSS files'
        },
        
        // Font files - cache for 1 year
        {
            pattern: /^\/assets\/fonts\/.+\.(woff|woff2|ttf|eot)$/,
            maxAge: 31536000, // 1 year
            immutable: true,
            description: 'Font files'
        },
        
        // Icons and small assets - cache for 3 months
        {
            pattern: /^\/assets\/.+\.(ico|svg)$/,
            maxAge: 7776000, // 3 months
            immutable: false,
            description: 'Icons and SVG files'
        }
    ]

    // Find matching cache config
    const matchedConfig = cacheConfigs.find(config => config.pattern.test(pathname))
    
    if (matchedConfig) {
        // Build cache control header
        let cacheControl = `public, max-age=${matchedConfig.maxAge}`
        
        if (matchedConfig.immutable) {
            cacheControl += ', immutable'
        }
        
        // Set cache headers
        setHeader(event, 'Cache-Control', cacheControl)
        setHeader(event, 'Vary', 'Accept-Encoding')

        // Add extra headers if specified
        if (matchedConfig.extraHeaders) {
            Object.entries(matchedConfig.extraHeaders).forEach(([key, value]) => {
                setHeader(event, key, value)
            })
        }

        // Add ETag for better caching
        if (!matchedConfig.immutable) {
            // For non-immutable assets, we can add ETag based on file path
            const etag = `"${Buffer.from(pathname).toString('base64')}"`
            setHeader(event, 'ETag', etag)
        }

        // Add expires header for better browser compatibility
        const expiresDate = new Date(Date.now() + matchedConfig.maxAge * 1000)
        setHeader(event, 'Expires', expiresDate.toUTCString())
        
        // Log cache configuration (only in development)
        if (process.env.NODE_ENV === 'development') {
            console.log(`🗂️ Cache headers set for ${pathname}:`, {
                config: matchedConfig.description,
                cacheControl,
                maxAge: `${matchedConfig.maxAge}s (${Math.round(matchedConfig.maxAge / 86400)} days)`
            })
        }
    }
})

// Helper function to get file extension
function getFileExtension(pathname: string): string {
    const parts = pathname.split('.')
    return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : ''
}

// Helper function to determine if file is likely to change
function isImmutableAsset(pathname: string): boolean {
    // Files with hash in name are immutable
    if (pathname.includes('/_nuxt/') && /\.[a-f0-9]{8,}\.(js|css)$/.test(pathname)) {
        return true
    }
    
    // Hero banner images are usually immutable
    if (pathname.includes('/hero-banner/')) {
        return true
    }
    
    // JavaScript and CSS files in assets are usually versioned
    if (pathname.match(/\/assets\/(js|css)\//)) {
        return true
    }
    
    return false
}
