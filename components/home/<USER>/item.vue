<template>
    <div class="game-item px-1 lg:px-2" @click="openGame(item, isLiveCasino, isIframe, TECH_PLAY_TYPES.some((value: number) => item.display_types.includes(value)))">
        <div class="relative overflow-hidden rounded-lg">
            <div class="relative pt-[68.66%]">
                <CommonImage
                    lazyload
                    classWrapper="!absolute inset-0 object-contain"
                    class="min-h-full min-w-full max-h-[100px] lg:max-h-[150px] !object-contain size-[65px]"
                    :src="item.image"
                    :imageDefault="`${staticUrl}/pages/news/loading.gif`"
                    alt="thumbnail"
                    loading="lazy"
                    fetchpriority="low"
                    isDefaultImageClass
                />
                <div class="effect">
                    <button
                        v-if="isDesktop"
                        class="btn-play h-11 w-[9.1875rem] items-center justify-center gap-x-2.5 rounded-lg bg-z-red-dit"
                    >
                        <NuxtIcon name="play" class="text-2xl" />
                        <span
                            class="tex-white text-sm font-semibold uppercase"
                            >{{ $t('common.bet_now') }}</span
                        >
                    </button>
                </div>
                <div
                    v-if="jackpots[item?.partner_game_id]"
                    class="absolute left-1 top-1 flex max-w-[10rem] items-center gap-x-1 overflow-hidden rounded-[100px] bg-[#1D1D1D99] px-1.5 py-0.5 [backdrop-filter:blur(4px)] sm:max-w-[72%]"
                >
                    <img
                        :src="`${staticUrl}/icons/icon-hu.svg`"
                        class="h-3 w-3"
                        alt="icon money"
                    />
                    <div class="flex items-center">
                        <CommonAnimatedNumber
                            class="info__jackpot !text-xs !text-[#FFCB41]"
                            :number="jackpots[item?.partner_game_id]"
                            :previousNumber="
                                previousJackpots[item?.partner_game_id]
                            "
                        />
                    </div>
                </div>
            </div>
            <div class="info relative cursor-pointer">
                <NuxtIcon
                    :name="item.is_favorite ? 'favorite' : 'un-favorite'"
                    class="icon-favorite absolute right-0 top-1 cursor-pointer"
                    @click.stop="handleBookMark(item, games, 'game')"
                />
                <p
                    class="info__title mb-0.5 text-[11px] font-medium leading-[14px] text-white lg:mb-1.5 lg:text-sm lg:leading-5"
                >
                    {{ item.name }}
                </p>
                <p
                    class="mb-0.5 text-[9px] font-normal capitalize leading-4 text-[#E6E6E6] lg:mb-1.5 lg:text-xs"
                >
                    {{ item.partner_txt?.toString()?.toLowerCase() }}
                </p>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { usePlayGame } from '~/composables/use-play-game'
import type { IGame } from '~/interfaces/game'
import { TECH_PLAY_TYPES } from '~/constants/game'

defineProps({
    games: {
        type: Array as () => IGame[],
        default: () => {},
    },
    item: {
        type: Object as () => IGame,
        default: () => {},
    },
})

const staticUrl = useRuntimeConfig().public.staticUrl
const { isDesktop } = useDevice()
const { openGame } = usePlayGame()
const socketStoreInstance = useSocket()
const { jackpots, previousJackpots } = storeToRefs(socketStoreInstance)
const { handleBookMark } = useFavorite()
const isLiveCasino = ref(false)
const isIframe = ref(false)
</script>

<style scoped>
.info {
    @apply px-0.5 py-1.5;
    &__title {
        @apply max-w-[calc(100%_-_2rem)] overflow-hidden text-ellipsis xs:max-w-[calc(100%_-_2.5rem)];
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    }
    &__jackpot {
        @media (max-width: 991px) {
            font-size: 12px !important;
            font-weight: 400 !important;
            line-height: 16px !important;
        }
    }
}
.effect {
    @apply absolute -bottom-[5px] left-0 z-[1] hidden h-full w-full rounded-lg  lg:content-[''];
    background: linear-gradient(180deg, rgba(20, 20, 20, 0) 0%, #141414 100%);
}
.name {
    @apply mb-3 line-clamp-2 overflow-hidden text-ellipsis text-center text-sm font-bold uppercase leading-[1.21429] text-white;
}
.game-item:hover .effect {
    @apply translate-y-0 lg:flex;
    .btn-play {
        @apply absolute bottom-2/4 left-2/4 z-10 flex -translate-x-2/4 translate-y-2/4 animate-[0.3s_ease-in-out_0s_scrollUp];
    }
}
:deep(.v-lazy-image) {
    @apply m-auto w-[30%];
}
:deep(.base-image .v-lazy-image-loaded) {
    @apply m-auto w-full rounded-lg transition-[unset];
}
@keyframes scrollUp {
    0% {
        opacity: 0;
        bottom: 30%;
    }
    50% {
        opacity: 0.5;
        bottom: 40%;
    }
    100% {
        opacity: 1;
        bottom: 50%;
    }
}
</style>
