<template>
    <div class="hero-banner cursor-pointer">
        <div class="relative">
            <CommonImage
                @click="onClickHeroBanner"
                class="size-full cursor-pointer"
                :src="`${staticUrl}/home/<USER>/jackpot/pc.jpg`"
                :srcMb="`${staticUrl}/home/<USER>/jackpot/mb.jpg`"
                alt="Jackpot Hero Banner"
                loading="eager"
                fetchpriority="high"
                width="1920"
                height="600"
                preset="hero"
                sizes="100vw"
            />
            <div
                class="absolute left-[5%] top-[7.5%] flex flex-col items-center justify-center lg:left-[13.5%] lg:top-[1.8125rem]"
            >
                <CommonImage
                    @click="onClickHeroBanner"
                    class="h-[2.125rem] w-[3.8125rem] lg:h-[5.25rem] lg:w-[9.25rem] mx-auto"
                    :max="`(max-width: 991px)`"
                    :src="`${staticUrl}/home/<USER>/jackpot/go88.png`"
                    :srcMb="`${staticUrl}/home/<USER>/jackpot/go88-mb.png`"
                    alt="logo"
                    loading="lazy"
                    :fetchpriority="'low'"
                />
                <div
                    @click="onClickHeroBanner"
                    class="mt-0 h-[3.185rem] w-[13.4375rem] text-center lg:mt-2 lg:h-[7rem] lg:w-[32.1875rem]"
                >
                    <CommonImage
                        class="w-[13.4375rem] lg:w-[32.1875rem]"
                        :max="`(max-width: 991px)`"
                        :src="`${staticUrl}/home/<USER>/jackpot/title-pc.png`"
                        :srcMb="`${staticUrl}/home/<USER>/jackpot/title-mb.png`"
                        alt="title"
                        loading="lazy"
                        :fetchpriority="'low'"
                    />
                </div>
                <div
                    v-show="jackpotGo88?.length"
                    class="jackpot lightAnimation bottom-[4vw] -translate-x-[0.5rem] left-3 mt-2 h-[3.0625rem] w-[13.4375rem] sm:left-[5%] md:h-[4.625rem] md:w-[22rem] lg:bottom-[15%] lg:left-[17.7%] lg:mt-[1.875rem] lg:h-[5.5rem] lg:w-[24.625rem]"
                >
                    <Swiper
                        ref="swiperRef"
                        :key="swiperKey"
                        :modules="[
                            Autoplay,
                            EffectCards,
                            EffectCreative,
                            FreeMode,
                            EffectFade,
                        ]"
                        :loop="true"
                        :slides-per-view="'auto'"
                        :spaceBetween="20"
                        :fadeEffect="{ crossFade: true }"
                        :autoplay="{
                            delay: 3000,
                            disableOnInteraction: false,
                            pauseOnMouseEnter: true,
                        }"
                        :nested="true"
                        @swiper="onSwiper"
                        @slideChange="onSlideChange"
                    >
                        <SwiperSlide
                            v-for="(item, index) in jackpotGo88"
                            :key="index"
                            @click="
                                openGame(
                                    item.game,
                                    item.name.includes('live'),
                                    false
                                )
                            "
                        >
                            <div
                                class="jackpot-item relative h-[3.5rem] w-[13.4375rem] md:h-[4.625rem] md:w-[22rem] lg:h-[5.5rem] lg:w-[24.625rem]"
                            >
                                <img
                                    class="w-[13.4375rem] md:w-[22rem] lg:w-[24.625rem]"
                                    :src="`${staticUrl}/home/<USER>/jackpot/${item.name}.avif`"
                                    :alt="item.name"
                                    loading="lazy"
                                    fetchpriority="low"
                                />
                                <div
                                    class="absolute bottom-3 left-0 pl-10 lg:pl-16 flex w-[100%] items-center justify-center lg:w-[24.625rem]"
                                >
                                    <CommonAnimatedNumber
                                        className="custom-number !font-open_sans font-extrabold lg:!text-[1.875rem] lg:tracking-[-1px] md:!text-[1.5rem] !text-[0.9375rem] !leading-[calc(28/15)] lg:leading-[calc(40/30)]"
                                        :number="item.jackpot"
                                        :previousNumber="
                                            prevJackpotGo88[index]?.jackpot
                                                ? +prevJackpotGo88[index]
                                                      ?.jackpot
                                                : 0
                                        "
                                        isHeroBanner
                                        suffix="VND"
                                    />
                                </div>
                            </div>
                        </SwiperSlide>
                    </Swiper>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {
    Autoplay,
    EffectCards,
    EffectCreative,
    FreeMode,
    EffectFade,
} from 'swiper/modules'
import { PAGE_URL } from '~/constants/page-urls'
import type SwiperCore from 'swiper'
import { usePlayGame } from '~/composables/use-play-game'

const swiperRef = ref<SwiperCore | null>(null)

const socketStoreInstance = useSocket()
const { jackpotGo88, prevJackpotGo88 } = storeToRefs(socketStoreInstance)

const { staticUrl } = useRuntimeConfig().public
const router = useRouter()
const { openGame } = usePlayGame()

const swiperKey = ref(0)
const activeSlideIndex = ref(0)

const onSwiper = (swiperInstance) => {
    swiperRef.value = swiperInstance
}
const onSlideChange = (swiper) => {
    if (swiperRef.value) {
        activeSlideIndex.value = swiper.realIndex
    }
}
const onClickHeroBanner = () => {
    openGame(
        jackpotGo88.value[activeSlideIndex.value]?.game,
        jackpotGo88.value[activeSlideIndex.value]?.name.includes('live'),
        false
    )
}

watch(
    () => jackpotGo88.value,
    (jackpots) => {
        const jackpotsGameIds = jackpots?.map((item) => item.gameId) || []

        const isDifferent =
            jackpots?.length &&
            (jackpots?.length > prevJackpotGo88.value?.length ||
                prevJackpotGo88.value.some(
                    (item) => !jackpotsGameIds.includes(item.gameId)
                ))

        if (isDifferent) {
            swiperKey.value += 1
            if (typeof window !== 'undefined') {
                window.scrollTo(0, 0)
            }
        }
    },
    { immediate: true, deep: true }
)
</script>

<style scoped>
.hero-banner {
    /* Prevent layout shift by setting aspect ratio */
    aspect-ratio: 1920 / 600;
    min-height: 200px;
}

@media (max-width: 991px) {
    .hero-banner {
        aspect-ratio: 430 / 195;
        min-height: 150px;
    }
}

/* Optimize for LCP - ensure image loads immediately */
.hero-banner :deep(.base-image) {
    display: block;
    width: 100%;
    height: 100%;
}

/* Optimize jackpot animation performance */
.jackpot.lightAnimation {
    will-change: transform;
    transform: translateZ(0);
}
</style>

