<template>
    <div class="relative z-0 mt-7 text-xl lg:mt-12">
        <h2
            class="mb-3 flex items-center justify-between text-base font-semibold leading-6 text-white lg:mb-6 lg:text-[22px] lg:leading-[26.82px]"
        >
            <span>
                {{ $t('home.slot_title') }}
            </span>
            <nuxt-link
                class="inline-flex items-center gap-x-0.5 lg:gap-x-[6px]"
                to="/cong-game/nohu"
            >
                <span
                    class="text-[11px] font-normal leading-[14px] text-[#AAAAAA] lg:text-sm lg:font-medium lg:leading-[17.07px] lg:hover:text-white"
                    >Xem thêm</span
                >
                <NuxtIcon
                    name="arrow-down"
                    class="flex -rotate-90 text-[11px] text-[#AAAAAA]"
                ></NuxtIcon>
            </nuxt-link>
        </h2>
        <div
            class="relative -ml-2 -mr-[0.938rem] pr-[0.938rem] lg:-mr-2.5 lg:pr-2.5"
        >
            <Swiper
                :modules="[
                    Autoplay,
                    Pagination,
                    EffectCards,
                    EffectCreative,
                    FreeMode,
                    Navigation,
                ]"
                :spaceBetween="0"
                :breakpoints="{
                    768: {
                        slidesPerView: 3.5,
                        spaceBetween: 0,
                    },
                    992: {
                        slidesPerView: 6.08,
                        spaceBetween: 0,
                    },
                }"
                :slidesPerView="2.4"
                :slidesPerGroup="1"
                :loop="true"
                :navigation="{
                    nextEl: '.btn-next',
                    prevEl: '.btn-prev',
                }"
                lazy
                :autoplay="{
                    delay: 3000,
                    disableOnInteraction: false,
                    pauseOnMouseEnter: true,
                }"
                loading="lazy"
            >
                <SwiperSlide
                    v-for="item in slotGames"
                    :key="item.partner_game_id"
                    loading="lazy"
                    class="max-w-[41.66%] md:max-w-[28.56%] lg:max-w-[16.447%]"
                >
                    <HomeSlotGameItem :item="item" :games="slotGames" />
                </SwiperSlide>
            </Swiper>
            <!--            <div-->
            <!--                class="btn-next absolute -right-[1px] top-0 z-100 hidden h-full w-[6vw] translate-y-0 cursor-pointer items-center justify-center pr-4 lg:flex"-->
            <!--            >-->
            <!--                <NuxtIcon class="ml-auto text-2xl" name="arrow-next"></NuxtIcon>-->
            <!--            </div>-->
            <!--            <div-->
            <!--                class="btn-prev absolute -left-[1px] top-0 z-100 hidden h-full w-[6vw] rotate-180 cursor-pointer items-center pr-4 lg:flex"-->
            <!--            >-->
            <!--                <NuxtIcon class="ml-auto text-2xl" name="arrow-next"></NuxtIcon>-->
            <!--            </div>-->
        </div>
    </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import {
    Autoplay,
    Pagination,
    EffectCards,
    EffectCreative,
    FreeMode,
    Navigation,
} from 'swiper/modules'
import { useSlotGameStore } from '~/composables/games/use-slot-games'
const useSlotGameStoreInstance = useSlotGameStore()
const { getSlotGames } = useSlotGameStoreInstance
const { slotGames } = storeToRefs(useSlotGameStoreInstance)

await useAsyncData('slotGames', () =>
    getSlotGames({
        page: 1,
        limit: 21,
    })
)
</script>

<style scoped>
.btn-next {
    background: linear-gradient(
        270deg,
        #161312 28.82%,
        rgba(22, 19, 18, 0.62) 63.63%,
        rgba(22, 19, 18, 0) 95.07%
    );
}
.btn-prev {
    background: linear-gradient(
        270deg,
        #161312 25.9%,
        rgba(22, 19, 18, 0.71) 57.69%,
        rgba(22, 19, 18, 0) 95.07%
    );
}
.title {
    @apply mx-2.5 my-0 bg-clip-text text-[1.313rem] font-extrabold uppercase leading-6;
    background: linear-gradient(183.37deg, #fff 30.93%, #ffd256 92.45%);
    -webkit-background-clip: text;
    filter: drop-shadow(0.38699px 0.38699px 0 #ff8100);
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
}
</style>
