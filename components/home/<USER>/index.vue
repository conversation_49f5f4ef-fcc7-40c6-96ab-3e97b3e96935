<template>
    <div class="hot-game mt-6 lg:mt-12 max-md:aspect-[399.98/553.06]">
        <h3 class="text-base leading-6 lg:text-[22px] font-semibold lg:leading-[26.82px] text-white mb-0 lg:mb-6">
            <span>{{ $t('home.hot_game_title') }}</span>
        </h3>
        <ul
            class="hot-game__list grid grid-cols-2 gap-2 lg:grid-cols-4 lg:gap-4 text-white"
        >
            <li v-if="innerWidth > 767"
                class="relative rounded-md col-start-1 col-end-3 hidden md:h-[12rem] cursor-pointer bg-cover bg-no-repeat md:block"
                :style="{
                    backgroundImage: `url(/assets/images/home/<USER>/${featureHotGame.bg})`,
                }"
                @click="openLinkHotGame(featureHotGame)"
            >
                <div
                    class="img absolute bottom-0 right-5 h-auto w-[46%] object-contain md:bottom-3  lg:-bottom-1 md:h-[12rem] lg:h-[13.125rem]"
                >
                    <CommonImage
                        :src="`${staticUrl}/home/<USER>/${featureHotGame.thumb}`"
                        alt="logo"
                        class="size-full object-contain md:max-h-[12.83rem]"
                        loading="lazy"
                        fetchpriority="low"
                    />
                </div>
                <div
                    class="absolute bottom-0 left-0 top-0 flex flex-col py-6 pl-8"
                >
                    <h4 class="text-lg font-bold">
                        {{ $t(featureHotGame.title) }}
                    </h4>
                    <p class="text-base">{{ $t(featureHotGame.slogan) }}</p>
                    <button
                        class="mt-auto flex h-9 w-[9.063rem] items-center justify-center gap-x-2 rounded-lg bg-[#FF4700] text-normal text-white font-semibold uppercase"
                    >
                        {{ $t('common.bet_now') }} <NuxtIcon name="arrow-right-w" class="text-[20px]"></NuxtIcon>
                    </button>
                </div>
            </li>
            <li
                v-else
                class="col-start-1 col-end-3 block relative pt-[calc(161/343*100%)] md:hidden"
                @click="openLinkHotGame(featureHotGame)"
            >
                <CommonImage
                    :src="`${staticUrl}/home/<USER>/${featureHotGame.thumbMb}`"
                    alt="logo"
                    class="size-full"
                    classWrapper="!absolute inset-0"
                    loading="lazy"
                    fetchpriority="low"
                />
            </li>
            <li
                v-for="(game, index) in hotGames"
                :key="index"
                @click="openLinkHotGame(game)"
                class="relative h-[106px] cursor-pointer rounded-lg bg-cover bg-no-repeat md:h-[11.875rem] overflow-hidden"
                :style="{
                    backgroundImage: `url(/assets/images/home/<USER>/${game.bg})`,
                }"
            >
                <div
                    class="img absolute right-1 bottom-0 flex items-end h-[106px] w-[60%] object-contain md:w-[11.5rem] md:h-[11.875rem] md:right-5"
                >
                    <CommonImage
                        :src="`${staticUrl}/home/<USER>/${game.thumb}`"
                        alt="logo"
                        class="size-full max-h-full object-contain"
                        classWrapper="size-full max-h-[95%]"
                        loading="lazy"
                        fetchpriority="low"
                    />
                </div>
                <h4 class="title z-10">{{ $t(game.title) }}</h4>
                <button class="bet-btn hidden md:inline-block">
                    {{ $t('common.bet_now') }} <NuxtIcon name="arrow-right-w" class="text-[20px]"></NuxtIcon>
                </button>
                <img
                    v-if="game.isLive"
                    :src="`${staticUrl}/home/<USER>/live-casino.svg`"
                    alt="logo"
                    class="icon-live absolute left-[3%] top-[3%] w-[10%]"
                    loading="lazy"
                    fetchpriority="low"
                />
            </li>
        </ul>
    </div>
</template>

<script setup lang="ts">
import { useUserStore } from '~/composables/use-user'
import {
    hotGames,
    featureHotGame,
    type IGameHot,
} from '~/resources/home/<USER>'
import { useModalStore } from '~/stores'
import { IFRAME_ENDPOINT } from '~/constants/api-endpoint'

const useModalStoreInstance = useModalStore()
const useUserStoreInstance = useUserStore()
const { innerWidth  } = useWindowSize()
const { showLoginModal } = storeToRefs(useModalStoreInstance)
const staticUrl = useRuntimeConfig().public.staticUrl
const { isLogged } = storeToRefs(useUserStoreInstance)
const { openLink, playGameByApiUrl } = usePlayGame()
const { isMobile } = useDevice()
const router = useRouter()
const openLinkHotGame = (item: IGameHot) => {
    if (item.request && item.url === '/e-sports') {
        if (!isLogged.value) {
            showLoginModal.value = true
            return
        }
        playGameByApiUrl(`${IFRAME_ENDPOINT['e-sports'].apiUrl}`)
        return
    }
    if (isMobile && item.newTab) {
        playGameByApiUrl(`${IFRAME_ENDPOINT[item.urlMB as string].apiUrl}`)
        return
    }
    router.push(item.url)
}
</script>

<style lang="scss" scoped>
.text-gradient {
    @apply mb-2 text-center text-xl font-extrabold md:mb-8 md:text-[1.375rem];
    span {
        background: linear-gradient(183deg, #fff 30.93%, #ffd256 92.45%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
}

li {
    @include pc() {
        &:hover {
            .img {
                transform: scale(1.05) translateY(-5px);
            }
            .title {
                bottom: 36%;
            }
            .bet-btn {
                @apply translate-y-0 opacity-100 delay-[0.2s];
            }
        }
        .img {
            transition: all 0.3s linear;
        }
    }
    .title {
        @apply absolute bottom-[10%] left-[5%] max-w-24  text-xs font-medium md:font-bold uppercase text-white transition-all duration-[0.3s] ease-[ease-in] md:bottom-6 md:left-6 md:max-w-fit md:text-lg md:leading-[22px];
    }
    .bet-btn {
        @apply absolute bottom-6 left-6 translate-y-2.5 transition-[0.3s] opacity-0 flex h-9 w-[9.063rem] items-center justify-center gap-x-2 rounded-lg bg-[#FF4700] text-white font-semibold uppercase;
    }
}
.icon-live {
    animation: fadein 0.7s infinite;
}
</style>
