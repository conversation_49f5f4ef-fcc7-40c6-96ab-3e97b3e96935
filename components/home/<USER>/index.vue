<template>
    <div
        class="providers-container flex h-[3.375rem] items-center overflow-hidden border-b border-solid border-b-z-chroma-black bg-z-dynamic-black lg:h-[6rem]"
        role="region"
        aria-label="Game Providers"
    >
        <div class="container cursor-grab">
            <div v-if="showMobile" class="hide-scroll flex items-center gap-x-4 overflow-auto">
                <ul
                    v-for="(chunk, chunkIdx) in providersChunked" :key="chunkIdx"
                    ref="providersElement"
                    class="flex items-center gap-x-4"
                    @touchmove="moveDrag"
                    @mousedown="onMousedown"
                    @mouseleave="onMouseleave"
                    @mouseup="onMouseleave"
                    @mousemove="onMousemove"
                >
                    <li
                        class="h-8 min-w-[5.8125rem] lg:h-10 lg:min-w-[7.25rem]"
                        v-for="(provider, index) in chunk"
                        :key="index"
                    >
                        <a
                            target="_blank"
                            :href="GO88_URL"
                            rel="noopener noreferrer"
                            :title="$t('common.go88_slogan')"
                            v-if="provider.alt === 'go88'"
                        >
                            <img
                                class="pointer-events-none h-8 w-[5.8125rem] lg:h-10 lg:w-[7.25rem]"
                                :width="provider.width"
                                :height="provider.height"
                                :src="`${staticUrl}/home/<USER>/${provider.thumb}`"
                                :alt="provider.alt"
                                loading="lazy"
                                fetchpriority="low"
                                decoding="async"
                                :style="{ aspectRatio: `${provider.width}/${provider.height}` }"
                            />
                        </a>
                        <div v-else>
                            <img
                                class="pointer-events-none h-8 w-[5.8125rem] lg:h-10 lg:w-[7.25rem]"
                                :width="provider.width"
                                :height="provider.height"
                                :src="`${staticUrl}/home/<USER>/${provider.thumb}`"
                                :alt="provider.alt"
                                loading="lazy"
                                fetchpriority="low"
                            />
                        </div>
                    </li>
                </ul>
            </div>
            <div v-else>
                <ul
                    ref="providersElement"
                    class="flex items-center gap-x-4"
                    @touchmove="moveDrag"
                    @mousedown="onMousedown"
                    @mouseleave="onMouseleave"
                    @mouseup="onMouseleave"
                    @mousemove="onMousemove"
                >
                    <li
                        class="h-8 min-w-[5.8125rem] lg:h-10 lg:min-w-[7.25rem]"
                        v-for="(provider, index) in providers"
                        :key="index"
                    >
                        <a
                            target="_blank"
                            :href="GO88_URL"
                            rel="noopener noreferrer"
                            :title="$t('common.go88_slogan')"
                            v-if="provider.alt === 'go88'"
                        >
                            <img
                                class="pointer-events-none h-8 w-[5.8125rem] lg:h-10 lg:w-[7.25rem]"
                                :width="provider.width"
                                :height="provider.height"
                                :src="`${staticUrl}/home/<USER>/${provider.thumb}`"
                                :alt="provider.alt"
                                loading="lazy"
                                fetchpriority="low"
                            />
                        </a>
                        <div v-else>
                            <img
                                class="pointer-events-none h-8 w-[5.8125rem] lg:h-10 lg:w-[7.25rem]"
                                :width="provider.width"
                                :height="provider.height"
                                :src="`${staticUrl}/home/<USER>/${provider.thumb}`"
                                :alt="provider.alt"
                                loading="lazy"
                                fetchpriority="low"
                            />
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { providers } from '~/resources/home/<USER>'
const { staticUrl, GO88_URL } = useRuntimeConfig().public

const providersElement = ref<HTMLElement | null>(null)
let isDragging = false
let startX = 0
let scrollLeft = 0

const { showMobile } = useCommon()

const onMousedown = (event: MouseEvent) => {
    if (window.innerWidth < 992) return
    isDragging = true
    startX = event.pageX - providersElement.value!.offsetLeft
    scrollLeft = providersElement.value!.scrollLeft
    providersElement.value!.style.cursor = 'grabbing'
}

const onMouseleave = () => {
    isDragging = false
    providersElement.value!.style.cursor = 'grab'
}

const onMousemove = (event: MouseEvent) => {
    if (!isDragging || !providersElement.value || window.innerWidth < 992)
        return
    event.preventDefault()
    const x = event.pageX - providersElement.value.offsetLeft
    const walk = (x - startX) * 1.5
    providersElement.value.scrollLeft = scrollLeft - walk
}

const moveDrag = (event: TouchEvent) => {
    if (!providersElement.value || window.innerWidth < 992) return
    const touch = event.touches[0]
    const x = touch.pageX - providersElement.value.offsetLeft
    const walk = (x - startX) * 1.5
    providersElement.value.scrollLeft = scrollLeft - walk
}

function chunkArray(array, size) {
    const chunked = []
    for (let i = 0; i < array.length; i += size) {
        chunked.push(array.slice(i, i + size))
    }
    return chunked
}

const providersChunked = computed(() => chunkArray(providers, 10))
</script>
