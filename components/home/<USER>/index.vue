<template>
    <div
        class="home-hero-banner relative pt-[45.82%] lg:pt-[24.75%]"
    >
        <div class="absolute inset-0">
            <Swiper
                ref="swiperRef"
                :modules="[
                    Autoplay,
                    Pagination,
                    EffectCards,
                    EffectCreative,
                    FreeMode,
                    Navigation,
                    EffectFade,
                ]"
                :slides-per-view="1"
                :spaceBetween="0"
                
                :loop="true"
                :pagination="{
                    clickable: true,
                }"
                :fadeEffect="{
                    crossFade: true,
                }"
                @slideChange="onChangeSlide"
                @swiper="onSwiper"
                :initial-slide="0"
            >
                <SwiperSlide
                    v-for="(heroBanner, index) in heroBanners"
                    :key="index"
                >
                    <component
                        v-if="currentSlideActive === index && !isMounted"
                        :is="heroBanner"
                    />
                    <component v-if="isMounted" :is="heroBanner" />
                </SwiperSlide>
            </Swiper>
        </div>
    </div>
</template>
<script setup lang="ts">
import {
    Autoplay,
    Pagination,
    EffectCards,
    EffectCreative,
    FreeMode,
    Navigation,
    EffectFade,
} from 'swiper/modules'
import type SwiperCore from 'swiper'
// import HomeHeroBannerInsuranceSport from '~/components/home/<USER>/insurance-sport/index.vue'
// import HomeHeroBannerKeno from '~/components/home/<USER>/keno/index.vue'
// import HomeHeroBannerGameCard from '~/components/home/<USER>/game-card/index.vue'
// import HomeHeroBannerP2p from '~/components/home/<USER>/p2p/index.vue'
// import HomeHeroBannerWithdrawCrypto from '~/components/home/<USER>/withdraw-crypto/index.vue'
// import HomeHeroBannerNumberGame5 from '~/components/home/<USER>/number-game-5/index.vue'
// import HomeHeroBannerSport24 from '~/components/home/<USER>/sport-24/index.vue'
import HomeHeroBannerCasino from '~/components/home/<USER>/casino/index.vue'
import HomeHeroBannerBonus from '~/components/home/<USER>/bonus-200/index.vue'
// import HomeHeroBannerCockfight from '~/components/home/<USER>/cockfight/index.vue'
import HomeHeroBannerVIPClub from '~/components/home/<USER>/vip-club/index.vue'
// import HomeHeroBannerLiveStream from '~/components/home/<USER>/live-stream/index.vue'
// import HomeHeroBannerFreeSpin from '~/components/home/<USER>/free-spin/index.vue'
// import HomeHeroBannerRaceTop from '~/components/home/<USER>/race-top/index.vue'
import HomeHeroBannerJackpot from '~/components/home/<USER>/jackpot/index.vue'
// import HomeHeroBannerVeSoCao from '~/components/home/<USER>/ve-so-cao/index.vue'
import HomeHeroBannerAnhTai from '~/components/home/<USER>/anh-tai/index.vue'
// import HomeHeroBannerChoTet from '~/components/home/<USER>/cho-tet/index.vue'
// import HomeHeroBannerGoldenStarWarrios from '~/components/home/<USER>/golden-star-warrios/index.vue'
// import HomeHeroBannerClubWorldCup from '~/components/home/<USER>/club-world-cup/index.vue'
// import HomeHeroBannerUEFAChampionLeagueRefund from '~/components/home/<USER>/uefa-champion-league-refund/index.vue'
import BannerDanhDeMienPhi from '~/components/home/<USER>/banner-danh-de-mien-phi/index.vue'
import HomeHeroBannerBaccarrat from '~/components/home/<USER>/baccarrat/index.vue'
// import HomeHeroBannerFifaClubRefund from '~/components/home/<USER>/fifa-club-refund/index.vue'


const swiperRef = ref<SwiperCore | null>(null)
const useUserStoreInstance = useUserStore()
const { user } = storeToRefs(useUserStoreInstance)

const socketStoreInstance = useSocket()
const { jackpotGo88, prevJackpotGo88 } = storeToRefs(socketStoreInstance)
const fullBanners = computed(() =>
    jackpotGo88.value?.length
        ? [
            HomeHeroBannerJackpot,
            // HomeHeroBannerFifaClubRefund,
            BannerDanhDeMienPhi,
            // HomeHeroBannerUEFAChampionLeagueRefund,
            // HomeHeroBannerGoldenStarWarrios,
            // HomeHeroBannerVeSoCao,
            // HomeHeroBannerChoTet,
        //   HomeHeroBannerRaceTop,
        //   HomeHeroBannerFreeSpin,
            HomeHeroBannerVIPClub,
            HomeHeroBannerBonus,
            HomeHeroBannerBaccarrat,
            HomeHeroBannerCasino,
            HomeHeroBannerAnhTai
          ]
        : [
            // HomeHeroBannerFifaClubRefund,
            BannerDanhDeMienPhi,
            // HomeHeroBannerUEFAChampionLeagueRefund,
            // HomeHeroBannerGoldenStarWarrios,
            // HomeHeroBannerVeSoCao,
            // HomeHeroBannerChoTet,
        //   HomeHeroBannerRaceTop,
        //   HomeHeroBannerFreeSpin,
            HomeHeroBannerVIPClub,
            HomeHeroBannerBonus,
            HomeHeroBannerBaccarrat,
            HomeHeroBannerCasino,
            HomeHeroBannerAnhTai
          ]
)
const commisionBanners = computed(() =>
    jackpotGo88.value?.length
        ? [
            HomeHeroBannerJackpot,
            // HomeHeroBannerFifaClubRefund,
            BannerDanhDeMienPhi,
            // HomeHeroBannerUEFAChampionLeagueRefund,
            // HomeHeroBannerClubWorldCup,
            // HomeHeroBannerGoldenStarWarrios,
            // HomeHeroBannerVeSoCao,
            // HomeHeroBannerChoTet,
        //   HomeHeroBannerRaceTop,
        //   HomeHeroBannerFreeSpin,
            HomeHeroBannerVIPClub,
            HomeHeroBannerBaccarrat,
            HomeHeroBannerCasino,
            HomeHeroBannerAnhTai,
          ]
        : [
            // HomeHeroBannerFifaClubRefund,
            BannerDanhDeMienPhi,
            // HomeHeroBannerUEFAChampionLeagueRefund,
            // HomeHeroBannerClubWorldCup,
            // HomeHeroBannerGoldenStarWarrios,
            // HomeHeroBannerVeSoCao,
            // HomeHeroBannerChoTet,
        //   HomeHeroBannerRaceTop,
        //   HomeHeroBannerFreeSpin,
            HomeHeroBannerVIPClub,
            HomeHeroBannerBaccarrat,
            HomeHeroBannerCasino,
            HomeHeroBannerAnhTai,
          ]
)

const heroBanners = computed(() => {
    const baseBanners = !!user.value && user.value?.package_id === 1
        ? commisionBanners.value
        : fullBanners.value
    
    return [
        ...baseBanners,
    ]
})

const isMounted = ref(false)

const currentSlideActive = ref(0)

const onChangeSlide = (event: SwiperCore) => {
    currentSlideActive.value = event.realIndex
}

const onSwiper = (swiperInstance: SwiperCore) => {
    swiperRef.value = swiperInstance
}
onMounted(() => {
    nextTick(() => {
        isMounted.value = true
    })
})

watch(
    () => jackpotGo88.value,
    (jackpots) => {
        const jackpotsGameIds = jackpots?.map((item) => item.gameId) || []
        const isDifferent =
            jackpots?.length &&
            (jackpots?.length > prevJackpotGo88.value?.length ||
                prevJackpotGo88.value.some(
                    (item) => !jackpotsGameIds.includes(item.gameId)
                ))

        if (isDifferent) {
            if (typeof window !== 'undefined') {
                window.scrollTo(0, 0)
            }

            if (swiperRef.value) {
                swiperRef.value.slideTo(0)
            }
        }
    },
    { immediate: true, deep: true }
)
</script>
