<template>
    <CommonTextInput
        class="relative"
        inputClass="relative"
        v-model="model"
        :type="isShowPassword ? 'text' : 'password'"
        :isValidateSpace="isValidateSpace"
    >
        <template v-if="isShowSuffixIcon" #suffix-icon>
            <button
                @click="isShowPassword = !isShowPassword"
                type="button"
                aria-label="toggle"
                class="absolute right-4 top-2/4 -translate-y-2/4 cursor-pointer"
            >
                <NuxtIcon
                    :name="iconPassword"
                    alt="suffix-icon"
                    class="text-xl"
                />
            </button>
        </template>
    </CommonTextInput>
</template>

<script setup lang="ts">
defineProps({
    isShowSuffixIcon: {
        type: Boolean,
        default: false,
    },
    isValidateSpace: {
        type: Boolean,
        default: false,
    },
})
const model = defineModel()

const isShowPassword = ref(false)

const iconPassword = computed(() =>
    isShowPassword.value ? 'eye' : 'eye-slash'
)
</script>
