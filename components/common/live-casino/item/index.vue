<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useNuxtApp } from '#app'
import type { IGame, TokenInfo } from '~/interfaces/game'
import {
    PARTNER_GAME_ID,
    GAMES_DATA_CASINO,
    DEALER_LIVE,
} from '~/resources/live-casino'
import { usePlayGame } from '~/composables/use-play-game'
import { convertLogoLiveStream } from '~/utils/provider-livestream/index'
import { PROVIDER_LIVESTREAM } from '~/constants/game'
import { TECH_PLAY_TYPES } from '~/constants/game'
import { useNanoPlayer } from '~/composables/use-nanoplayer'

const { isDesktop } = useDevice()
const { $verifyToken, $loadNanoPlayer, $checkPlayerByDivId, $checkPlayerErrorByDivId, $showLivestreamThumbnailByDivId, $listenNetworkChange } = useNuxtApp()
const staticUrl = useRuntimeConfig().public.staticUrl
const { openGame } = usePlayGame()
const { handleBookMark } = useFavorite()
const route = useRoute()
const { showMobile } = useCommon()
const socketStoreInstance = useSocket()

const { isLostedInternet } = storeToRefs(socketStoreInstance)

const props = defineProps({
    games: {
        type: Array as () => IGame[],
        default: () => {},
    },
    item: {
        type: Object as () => IGame,
        default: () => {},
    },
    isLiveCasino: {
        type: Boolean,
        default: false,
    },
    isFavorite: {
        type: Boolean,
        default: false,
    },
    isLoadedVideo: {
        type: Boolean,
        default: false,
    },
    jackpots: {
        type: Object as () => Record<string, number>,
        default: () => ({}),
    },
    previousJackpots: {
        type: Object as () => Record<string, number>,
        default: () => ({}),
    },
    viewers: {
        type: Array as () => { gameId: string; viewers: number }[],
        default: () => [],
    },
    previousViewers: {
        type: Array as () => { gameId: string; viewers: number }[],
        default: () => [],
    },
    isMuted: {
        type: Boolean,
        default: false,
    },
    page: {
        type: String,
        default: '',
    },
})

const { loadNanoPlayerOnVisible } = useNanoPlayer()

const emit = defineEmits(['toggleRadio'])

const isShowVideo = computed(() => {
    return (
        PARTNER_GAME_ID.includes(props.item.partner_game_id) &&
        props.isLoadedVideo
    )
})
const loadVideoItem = (group: any[], liveCasinoKey: string, tokenInfo: TokenInfo, domain: string) => {
    group.forEach((game) => {
        if (
            props.item?.partner_game_id === game.id &&
            props.item.partner_provider === game.partner
        ) {
            $verifyToken(
                liveCasinoKey,
                tokenInfo.id,
                tokenInfo.key,
                domain
            )
        }
    })
}

const loadVideo = () => {
    if (props.page !== 'home') return
    const tokenMap = new Map<string, TokenInfo>(GAMES_DATA_CASINO)
    const liveCasinoKey = `${props.item.partner_provider}_${props.item.partner_game_id}`
    const tokenInfo = tokenMap.get(liveCasinoKey)

    if (tokenInfo && (!$checkPlayerByDivId(`${props.item.partner_provider}_${props.item.partner_game_id}`) || $checkPlayerErrorByDivId(`${props.item.partner_provider}_${props.item.partner_game_id}`))) {
        // const domain = 'z01sv02.s2z.mooo.com'
        const domain = window.location.hostname

        const chunkedIds = []
        for (let i = 0; i < DEALER_LIVE.length; i += 6) {
            chunkedIds.push(DEALER_LIVE.slice(i, i + 6))
        }

        chunkedIds.forEach((group, index) => {
            loadVideoItem(group, liveCasinoKey, tokenInfo, domain)
        })
    }
}

watch(
    () => isLostedInternet.value,
    (newVal) => {
        if (newVal) {
            $showLivestreamThumbnailByDivId(`${props.item.partner_provider}_${props.item.partner_game_id}`)
        } else {
            loadVideo()
        }
    }
)

const handleVisibilityChange = () => {
    if (document.visibilityState === 'visible' && showMobile.value) {
        loadVideo()
    }
}

onMounted(async () => {
    await nextTick(async () => {
        if (import.meta.client) {
            setTimeout(() => {
                loadNanoPlayerOnVisible(document.querySelector('.game__item__thumb--video') as HTMLElement)
                $listenNetworkChange()
            }, 3000)
            setTimeout(() => {
                loadVideo()
            }, 4000)
        }
    })
    document.addEventListener('visibilitychange', handleVisibilityChange)
})

onBeforeUnmount(() => {
    document.removeEventListener('visibilitychange', handleVisibilityChange)
})
</script>
<template>
    <div class="game tabs__custom cursor-pointer">
        <div :class="['game-item', { 'effect-hover': !isShowVideo }]">
            <div
                @click.prevent="openGame(item, isLiveCasino)"
                v-if="item.isLabel"
                class="game__label-partner"
            >
                <div class="game__label-partner--image">
                    <img
                        :src="`${staticUrl}/live-casino/label_casino.png`"
                        :alt="item.name"
                        loading="lazy"
                        fetchpriority="low"
                    />
                </div>
            </div>

            <div
                class="game__item__thumb game__item__thumb--video relative"
                :id="`${item.partner_provider}_${item.partner_game_id}`"
                v-if="isShowVideo"
                @click.prevent="openGame(item, isLiveCasino)"
            >
                <CommonImage
                    lazyload
                    classWrapper="inset-0 object-contain"
                    class="img-video-custom"
                    :src="route?.path === '/' ? `${staticUrl}/home/<USER>/${item?.partner_game_id}-preview.webp` : item.image"
                    :imageDefault="`${staticUrl}/games/${useRuntimeConfig().public.BRAND_NAME?.toLowerCase()}_image-default.avif`"
                    :onerror="`${staticUrl}/loading.gif`"
                    :alt="item.name"
                    loading="lazy"
                    fetchpriority="low"
                />
                <div
                    :class="[
                        'absolute bottom-3.5 z-[10] logo-partner',
                        item.partner === 'go' ? ' right-1.5' : ' right-0.5',
                        item.partner
                    ]"
                    v-if="
                        PROVIDER_LIVESTREAM.includes(
                            item.partner?.toString()?.toLowerCase()
                        )
                    "
                >
                    <img
                        class="!lg:h-[1.875rem] !lg:w-[3.96875rem] !h-[1.25rem] !w-auto"
                        :src="`${staticUrl}/live-casino/livestream/${convertLogoLiveStream(
                            item.partner.toString().toLowerCase()
                        )}`"
                        :alt="item.partner"
                        loading="lazy"
                        fetchpriority="low"
                    />
                </div>

                <div
                    class="hunt-money-video z-[5]"
                    v-if="
                        jackpots[`${item?.partner}_${item?.partner_game_id}`]
                    "
                    @click.prevent="
                        !isDesktop &&
                            openGame(
                                item,
                                isLiveCasino,
                                false,
                                TECH_PLAY_TYPES.some((value: number) =>
                                    item.display_types.includes(value)
                                )
                            )
                    "
                >
                    <img
                        :src="`${staticUrl}/icons/icon-hu.svg`"
                        class="!size-3 lg:!size-4"
                        alt="icon money"
                    />
                    <div class="hunt-money-video__value">
                        <CommonAnimatedNumber
                            :number="
                                jackpots[
                                    `${item?.partner}_${item?.partner_game_id}`
                                ]
                            "
                            :previousNumber="
                                previousJackpots[
                                    `${item?.partner}_${item?.partner_game_id}`
                                ]
                            "
                        />
                    </div>
                </div>
            </div>
            <nuxt-link
                v-else
                to=""
                class="game__item__thumb"
                @click.prevent="!isDesktop && openGame(item, isLiveCasino)"
            >
                <!-- <img
                    :src="item.image"
                    :onerror="`${staticUrl}/games/image-default.png`"
                    :alt="item.name"
                /> -->
                <CommonImage
                    lazyload
                    classWrapper="inset-0 object-contain"
                    class="h-full w-full"
                    :src="item.image"
                    :imageDefault="`${staticUrl}/loading.gif`"
                    :alt="item.name"
                />
                <CommonButton
                    v-if="isDesktop"
                    type="button"
                    @click.prevent="openGame(item, isLiveCasino)"
                    class="btn-play h-11 w-[9.1875rem] items-center justify-center gap-x-2.5 rounded-lg bg-z-red-dit"
                >
                    <NuxtIcon name="play" class="text-2xl" />
                    <span class="tex-white text-sm font-semibold uppercase">{{
                        $t('common.bet_now')
                    }}</span>
                </CommonButton>
            </nuxt-link>
            <div class="game__item__content">
                <div
                    class="game-name"
                    @click.prevent="!isDesktop && openGame(item, isLiveCasino)"
                >
                    <div class="name-item" :title="item.name">
                        {{ item.name }}
                    </div>
                    <p class="partner-item">
                        {{ item.partner_txt }}
                    </p>
                </div>
                <div class="favorite-item">
                    <NuxtIcon
                        :name="item.is_favorite ? 'favorite' : 'un-favorite'"
                        class="cursor-pointer text-2xl"
                        @click="
                            handleBookMark(item, games, 'casino', isFavorite)
                        "
                    />
                </div>
            </div>
            <div v-if="isShowVideo && route?.path === '/'" @click="emit('toggleRadio', item)" class="absolute left-[40px] top-[8px] z-[11] flex gap-1 js-radio">
                <img
                    :src="`${staticUrl}/icons/${isMuted ? 'on' : 'off'}.svg`"
                    alt="radio"
                    class="size-6 cursor-pointer"
                />
            </div>

            <div v-if="isShowVideo" class="absolute left-1 top-1 z-[5] flex gap-1 item-viewer-ct">
                <div class="item-viewer" v-if="viewers.find(
                                (e) =>
                                    e.gameId ===
                                    `${item?.partner}_${item?.partner_game_id}`
                            )?.viewers">
                    <CommonLiveStreamingAnimation v-if="route?.path === '/'"/>
                    <img
                        :src="`${staticUrl}/icons/viewer.svg`"
                        alt="viewer"
                        class="!size-4"
                    />
                    <CommonAnimatedNumber
                        :number="
                            viewers.find(
                                (e) =>
                                    e.gameId ===
                                    `${item?.partner}_${item?.partner_game_id}`
                            )?.viewers || 0
                        "
                        :previousNumber="
                            previousViewers.find(
                                (e) =>
                                    e.gameId ===
                                    `${item?.partner}_${item?.partner_game_id}`
                            )?.viewers || 0
                        "
                        className="!text-white !font-roboto !leading-[calc(16/12)] !translate-y-[1px]"
                    />
                </div>
            </div>

            <div
                class="hunt-money z-[1]"
                v-if="
                    jackpots[`${item?.partner}_${item?.partner_game_id}`] &&
                    !isShowVideo
                "
                @click.prevent="
                    !isDesktop &&
                        openGame(
                            item,
                            isLiveCasino,
                            false,
                            TECH_PLAY_TYPES.some((value: number) =>
                                item.display_types.includes(value)
                            )
                        )
                "
            >
                <img
                    :src="`${staticUrl}/icons/icon-hu.svg`"
                    class="h-3 w-3 lg:h-4 lg:w-4"
                    alt="icon money"
                />
                <div class="hunt-money__value">
                    <CommonAnimatedNumber
                        :number="
                            jackpots[
                                `${item?.partner}_${item?.partner_game_id}`
                            ]
                        "
                        :previousNumber="
                            previousJackpots[
                                `${item?.partner}_${item?.partner_game_id}`
                            ]
                        "
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.game__label-partner {
    @apply absolute right-[-1px] top-[-1px] z-10 flex size-full justify-end;
    &--image {
        @apply h-[calc(100%_-_2.5rem)] w-[90px] rounded-none;
        @media (max-width: 1024px) {
            width: clamp(1px, 19.46667vw, 73px);
        }
    }
}

.game__label-new {
    @apply absolute right-0 top-0 z-10 flex h-[1.125rem] w-[3.251rem] items-center justify-center rounded-[0_0.75rem_0_0.5rem] bg-[#059E0B] text-xs font-bold uppercase shadow-[1px_-3px_10px_rgba(0,0,0,0.2)] lg:h-[1.625rem] lg:w-[3.875rem] lg:text-sm;

    @media (min-width: 992px) {
        @apply h-[30px] w-[77px] text-[16px] leading-[20px];
    }
}

.game {
    @apply relative;

    &__item {
        @apply relative z-10 flex aspect-[166_/_115] h-full w-full content-between justify-between rounded-md bg-[#1d1d1d] md:aspect-[250_/_244];

        &__thumb {
            @apply block aspect-[250/_174] w-full rounded-xl after:absolute after:left-0 after:top-0 after:z-[-1] after:h-full after:w-full after:rounded-xl after:content-[''];

            img {
                @apply rounded-t-md object-cover;
            }
            video {
                @apply rounded-t-md;
            }

            @media (max-width: 1200px) {
                @apply max-h-full min-h-[7rem];
            }
        }

        @media (min-width: 1200px) {
            &__content {
                .game-name {
                    @apply items-start;
                }
            }
        }
    }
}
:deep(.base-image) {
    @apply flex aspect-[166/115] size-full items-center justify-center  lg:aspect-[250/174];
    .v-lazy-image {
        @apply mx-auto my-0 object-contain p-[25%];
        &.v-lazy-image-loaded {
            @apply m-auto aspect-[166/115] size-full object-cover p-0 lg:aspect-[250/174];
        }
    }
}
.item-viewer {
    @apply flex h-[20px] items-center gap-1 rounded bg-[#FFFFFF40] px-1 py-[0.125rem] backdrop-blur-[4.42px];
}
</style>
