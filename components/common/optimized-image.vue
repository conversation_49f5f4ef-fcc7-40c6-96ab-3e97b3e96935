<template>
    <picture class="optimized-image" :class="classWrapper">
        <!-- Mobile AVIF -->
        <source
            v-if="srcMb"
            :srcset="generateSrcSet(srcMb, 'avif', mobileSizes)"
            type="image/avif"
            :media="max"
            :sizes="mobileSizes"
        />
        <!-- Mobile WebP -->
        <source
            v-if="srcMb"
            :srcset="generateSrcSet(srcMb, 'webp', mobileSizes)"
            type="image/webp"
            :media="max"
            :sizes="mobileSizes"
        />
        <!-- Desktop AVIF -->
        <source
            :srcset="generateSrcSet(src, 'avif', desktopSizes)"
            type="image/avif"
            :sizes="desktopSizes"
        />
        <!-- Desktop WebP -->
        <source
            :srcset="generateSrcSet(src, 'webp', desktopSizes)"
            type="image/webp"
            :sizes="desktopSizes"
        />
        <!-- Fallback -->
        <img
            :src="src"
            :alt="alt"
            :class="imageClass"
            :width="width"
            :height="height"
            :loading="loading"
            :fetchpriority="fetchpriority"
            :sizes="sizes"
            @load="$emit('load')"
            @error="$emit('error')"
        />
    </picture>
</template>

<script setup lang="ts">
interface Props {
    src: string
    srcMb?: string
    alt: string
    width?: string | number
    height?: string | number
    loading?: 'lazy' | 'eager'
    fetchpriority?: 'high' | 'low' | 'auto'
    class?: string
    classWrapper?: string
    max?: string
    sizes?: string
    quality?: number
    preset?: 'hero' | 'thumbnail' | 'icon' | 'background'
}

const props = withDefaults(defineProps<Props>(), {
    loading: 'lazy',
    fetchpriority: 'auto',
    max: '(max-width: 991px)',
    sizes: '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw',
    quality: 80,
    preset: 'thumbnail'
})

defineEmits(['load', 'error'])

const imageClass = computed(() => props.class || '')

// Generate responsive sizes based on preset
const mobileSizes = computed(() => {
    switch (props.preset) {
        case 'hero':
            return '100vw'
        case 'thumbnail':
            return '(max-width: 640px) 50vw, 33vw'
        case 'icon':
            return '64px'
        case 'background':
            return '100vw'
        default:
            return props.sizes
    }
})

const desktopSizes = computed(() => {
    switch (props.preset) {
        case 'hero':
            return '100vw'
        case 'thumbnail':
            return '(max-width: 1024px) 50vw, 33vw'
        case 'icon':
            return '64px'
        case 'background':
            return '100vw'
        default:
            return props.sizes
    }
})

// Generate srcset with different sizes and formats
const generateSrcSet = (baseSrc: string, format: string, sizes: string) => {
    const baseUrl = baseSrc.replace(/\.[^/.]+$/, '')
    const widths = [320, 640, 768, 1024, 1280, 1920]
    
    return widths
        .map(width => {
            const quality = getQualityForPreset(props.preset)
            return `${baseUrl}_${width}w.${format}?quality=${quality} ${width}w`
        })
        .join(', ')
}

const getQualityForPreset = (preset: string) => {
    switch (preset) {
        case 'hero':
            return 85
        case 'thumbnail':
            return 75
        case 'icon':
            return 90
        case 'background':
            return 70
        default:
            return props.quality
    }
}
</script>

<style scoped>
.optimized-image {
    display: block;
    width: 100%;
    height: auto;
}

.optimized-image img {
    width: 100%;
    height: auto;
    display: block;
}
</style>
