<template>
    <div class="footer-nav hidden flex-[3] gap-x-3 lg:flex">
        <div
            class="nav-item flex-1"
            v-for="(linkInfoItem, index) in linkInfoItems"
            :key="index"
        >
            <div>
                <h4 class="mb-4 text-sm font-semibold capitalize text-white">
                    {{ linkInfoItem.title?.toLowerCase() }}
                </h4>
                <ul>
                    <li
                        v-for="(menuItem, i) in linkInfoItem.menuItems"
                        :key="i"
                        class="text-xs tracking-wide text-z-dark-gray h-[22px] flex items-center"
                    >
                        <a
                            :href="menuItem.link"
                            @click.prevent="moveTo(menuItem)"
                            rel="noopener noreferrer"
                            class="cursor-pointer h-full hover:text-white"
                        >
                            {{ menuItem.title }}
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { linkInfoItems } from '~/resources/footer'
import { useModalStore } from '~/stores'
import { type MenuItem } from '~/resources/footer'
const useModalStoreInstance = useModalStore()
const { showModalBankSupport } = storeToRefs(useModalStoreInstance)
const moveTo = (menuItem: MenuItem) => {
    if (menuItem.target === '#supportingbanks') {
        showModalBankSupport.value = true
    } else {
        const router = useRouter()
        router.push(menuItem.link)
    }
}
</script>

<style lang="scss" scoped></style>
