<template>
    <div class="footer-banks py-2 lg:mr-0 lg:py-4">
        <div class="container">
            <Swiper
                :modules="[Autoplay, EffectCards, EffectCreative, FreeMode]"
                :spaceBetween="8"
                :autoplay="{
                    delay: 3000,
                    disableOnInteraction: false,
                }"
                :breakpoints="{
                    992: {
                        slidesPerView: 'auto',
                        spaceBetween: 32,
                    },
                }"
                slides-per-view="auto"
                :slidesPerGroup="1"
                :loop="true"
            >
                <SwiperSlide
                    v-for="(item, index) in listBanksFooter"
                    :key="index"
                    class=""
                >
                    <img
                        :src="item.logo"
                        :alt="item.name"
                        loading="lazy"
                        :fetchpriority="'low'"
                        class="h-6 w-[5.625rem] object-contain lg:h-[1.875rem] lg:w-[5.625rem]"
                    />
                </SwiperSlide>
            </Swiper>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Autoplay, EffectCards, EffectCreative, FreeMode } from 'swiper/modules'
import { listBanksFooter } from '~/resources/footer'
</script>
