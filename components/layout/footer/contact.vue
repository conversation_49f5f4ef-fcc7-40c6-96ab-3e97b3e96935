<template>
    <div class="flex-1">
        <div class="mb-6 mt-6 flex items-center gap-x-4 lg:mt-0">
            <nuxt-link
                class="block h-10 w-[5.0625rem] lg:h-[3rem] lg:w-[9.375rem]"
                to="/"
            >
                <img
                    :src="`${staticUrl}/${
                        useRuntimeConfig().public.BRAND_NAME?.toLowerCase()
                    }_${
                        isDeskTop ? 'logo-pc.svg' : 'logo-mb.svg'
                    }`"
                    alt="Logo"
                    class="h-10 w-[5.0625rem] lg:h-[3rem] lg:w-[9.375rem]"
                />
            </nuxt-link>
            <img
                :src="`${staticUrl}/footer/pagcor.png`"
                alt="logo"
                class="block h-10 w-[3.06625rem] lg:hidden"
                loading="lazy"
                fetchpriority="low"
            />
        </div>
        <div
            class="mb-6 grid grid-cols-2 grid-rows-2 gap-x-2 gap-y-2 lg:mb-0 lg:flex lg:flex-col lg:gap-y-3"
        >
            <div
                v-for="(contactItem, index) in contactItems"
                :key="index"
                class="relative z-10 cursor-pointer"
            >
                <div>
                    <a
                        target="_blank"
                        :href="contactItem.link"
                        rel="noopener noreferrer"
                        class="flex items-center gap-x-2"
                        @click.prevent="showContact(contactItem.link)"
                    >
                        <img
                            class="h-5 w-5"
                            loading="lazy"
                            :alt="contactItem.logoAlt"
                            :src="`${staticUrl}${contactItem.logoSrc}`"
                        />
                        <div class="text-xs font-normal text-z-dark-gray">
                            {{ contactItem.title }}
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useLiveChat } from '~/stores'

const useLiveChatInstance = useLiveChat()
const { onClickLiveChat } = useLiveChatInstance
const {
    BRAND_NAME: brandName,
    staticUrl,
    CONTACT_TELEGRAM_LINK,
    CONTACT_MESSENGER_LINK,
    PROMOTION_TELEGRAM_LINK,
} = useRuntimeConfig().public

const { isDeskTop } = useWindowSize()

const showContact = (link: string) => {
    link === 'livechat' ? onClickLiveChat() : window.open(link, '_blank')
}
const contactItems = [
    {
        title: 'Live Chat',
        link: 'livechat',
        info: 'Hỗ Trợ 24/7',
        logoSrc: '/footer/live-chat.svg',
        logoAlt: 'live-chat',
        action: 'Live Chat',
        key: 'LIVECHAT',
        classes: 'w-[55%]',
    },
    {
        title: 'Telegram',
        info: `@${brandName}88HOTRO`,
        phone: 'SV88HOTRO',
        logoSrc: '/footer/tele.svg',
        logoAlt: 'mobile',
        action: 'Truy cập',
        key: 'TELE',
        link: CONTACT_TELEGRAM_LINK,
        classes: 'flex-1',
    },
    {
        title: 'Kênh thông tin KM',
        info: 'dinhcaobongdaSV88',
        phone: 'dinhcaobongdaSV88',
        logoSrc: '/footer/channel.svg',
        logoAlt: 'mobile',
        action: 'Truy cập',
        key: 'TELE',
        link: PROMOTION_TELEGRAM_LINK,
        classes: 'w-[55%]',
    },
    {
        title: 'FB Messenger',
        link: CONTACT_MESSENGER_LINK,
        info: 'Hỗ Trợ 24/7',
        logoSrc: '/footer/messenger.svg',
        logoAlt: 'messenger',
        action: 'Truy cập',
        key: 'MESSENGER',
        classes: 'flex-1',
    },
]
</script>
