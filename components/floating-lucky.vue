<template>
    <div
        v-if="isShowFloatingLucky"
        class="floating"
        :class="{ dragging: isDragging }"
        :style="{ left: position.x + 'px', top: position.y + 'px' }"
        @mousedown="startDrag"
        @touchstart="startDrag"
    >
        <div
            id="btn-close-floating-lucky"
            @click="closeFloatingLucky"
            class="absolute right-1 top-0 z-10 size-4 cursor-pointer"
        >
            <img
                :src="`${staticUrl}/floating-luckydraw/close.png`"
                alt="Đóng"
                class="h-4 w-4"
            />
        </div>
        <div @click="handleClick" @touchend="handleTouchEnd">
            <img
                :src="`${staticUrl}/floating-luckydraw/floating-lucky.webp`"
                alt="Đ<PERSON>h <PERSON>ễ<PERSON>"
                class="w-[90px] cursor-pointer"
                loading="lazy"
                fetchpriority="low"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { PAGE_URL } from '~/constants/page-urls'

const { staticUrl } = useRuntimeConfig().public
const { isMobile } = useDevice()
const isShowFloatingLucky = ref(true)

const isDragging = ref(false)
const dragOffset = ref({ x: 0, y: 0 })
const hasDragged = ref(false)
const dragStartPosition = ref({ x: 0, y: 0 })
const dragStartTime = ref(0)
const isScrollLocked = ref(false)

const position = ref({
    x: 0,
    y: 0,
})

onMounted(() => {
    setInitialPosition()
    document.addEventListener('mousemove', onDrag)
    document.addEventListener('mouseup', stopDrag)
    document.addEventListener('touchmove', onDrag, { passive: false })
    document.addEventListener('touchend', stopDrag)
    window.addEventListener('resize', constrainPosition)
})

onUnmounted(() => {
    document.removeEventListener('mousemove', onDrag)
    document.removeEventListener('mouseup', stopDrag)
    document.removeEventListener('touchmove', onDrag)
    document.removeEventListener('touchend', stopDrag)
    window.removeEventListener('resize', constrainPosition)
})

const setInitialPosition = () => {
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    const elementWidth = 90
    const elementHeight = 90

    position.value = {
        x: isMobile ? 16 : viewportWidth - elementWidth - 16,
        y: viewportHeight - elementHeight - (isMobile ? 68 : 50),
    }
}

const getEventCoordinates = (event: MouseEvent | TouchEvent) => {
    if ('touches' in event) {
        return {
            clientX: event.touches[0].clientX,
            clientY: event.touches[0].clientY,
        }
    }
    return {
        clientX: event.clientX,
        clientY: event.clientY,
    }
}

const startDrag = (event: MouseEvent | TouchEvent) => {
    const target = event.target as HTMLElement
    
    // Don't start drag if clicking on close button
    if (target.closest('#btn-close-floating-lucky')) {
        return
    }
    
    isDragging.value = true
    hasDragged.value = false
    dragStartTime.value = Date.now()

    const coords = getEventCoordinates(event)

    // Store initial drag position
    dragStartPosition.value = {
        x: coords.clientX,
        y: coords.clientY,
    }

    dragOffset.value = {
        x: coords.clientX - position.value.x,
        y: coords.clientY - position.value.y,
    }

    if (!isMobile || event.type === 'mousedown') {
        event.preventDefault()
    }
}

const onDrag = (event: MouseEvent | TouchEvent) => {
    if (!isDragging.value) return

    const coords = getEventCoordinates(event)

    const moveDistance = Math.sqrt(
        Math.pow(coords.clientX - dragStartPosition.value.x, 2) +
        Math.pow(coords.clientY - dragStartPosition.value.y, 2)
    )

    if (moveDistance > 8) {
        if (!hasDragged.value) {
            hasDragged.value = true
        }

        event.preventDefault()
        
        const newX = coords.clientX - dragOffset.value.x
        const newY = coords.clientY - dragOffset.value.y

        position.value = constrainToViewport(newX, newY)
    }
}

const stopDrag = () => {
    if (!isDragging.value) return
    
    isDragging.value = false
    
    if (isScrollLocked.value) {
        isScrollLocked.value = false
    }
}

const constrainToViewport = (x: number, y: number) => {
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    const elementWidth = 90
    const elementHeight = 90
    const headerHeight = 70

    const constrainedX = Math.max(0, Math.min(x, viewportWidth - elementWidth))
    const constrainedY = Math.max(
        headerHeight,
        Math.min(y, viewportHeight - elementHeight)
    )

    return {
        x: constrainedX,
        y: constrainedY,
    }
}

const constrainPosition = () => {
    position.value = constrainToViewport(position.value.x, position.value.y)
}

const handleClick = () => {
    if (!hasDragged.value) {
        navigateTo(PAGE_URL.LUCKY_DRAW)
    }
    
    // Reset state
    setTimeout(() => {
        hasDragged.value = false
        dragStartTime.value = 0
    }, 50)
}

const handleTouchEnd = (event: TouchEvent) => {
    if (!hasDragged.value) {
        const timeSinceStart = Date.now() - dragStartTime.value
        
        if (timeSinceStart < 300) {
            event.preventDefault()
            navigateTo(PAGE_URL.LUCKY_DRAW)
        }
    }
    
    // Reset state
    setTimeout(() => {
        hasDragged.value = false
        dragStartTime.value = 0
    }, 50)
}

const closeFloatingLucky = () => {
    isShowFloatingLucky.value = false
}
</script>

<style lang="scss" scoped>
.floating {
    @apply fixed z-[101] pt-[15px] cursor-pointer;
    touch-action: none;

    div > img {
        @apply block;
        animation: ping 8s cubic-bezier(0.15, 0.55, 0.15, 0.5) infinite;
    }

    &.dragging {
        div > img {
            animation: none;
        }
    }
}
</style>