import crypto from 'crypto'
import { createResolver } from '@nuxt/kit'
import sitemap from './sitemap.config'
// import { jsonData } from './constants/seo-schema-data'
import viteCompression from 'vite-plugin-compression'
const { resolve } = createResolver(import.meta.url)
require('dotenv').config()
const BUILDID = crypto.randomBytes(10).toString('hex')

const isProduction = process.env.NODE_ENV === 'production'

export default {
    pages: true,

    experimental: {
        payloadExtraction: false,
        treeshakeClientOnly: true,
        sharedPrerenderData: true,
        inlineSSRStyles: true, // Inline critical CSS
        componentIslands: true, // Enable component islands for better performance
        renderJsonPayloads: true, // Optimize JSON payloads
    },

    // Modern browser target to reduce legacy JavaScript
    // (build config is defined later in vite section)

    css: [
        '~/assets/scss/style.scss'
    ],

    postcss: {
        plugins: {
            tailwindcss: {},
            autoprefixer: {},
        },
    },

    modules: [
        'nuxt-lodash',
        'nuxt-icons',
        '@pinia/nuxt',
        '@pinia-plugin-persistedstate/nuxt',
        '@nuxtjs/i18n',
        'nuxt-swiper',
        '@nuxtjs/device',
        '@nuxtjs/google-fonts',
        '@nuxt/image',
        '@nuxt/ui', // included: @nuxtjs/tailwindcss, @nuxtjs/color-mode and nuxt-icon
        '@nuxtjs/sitemap',
        'nuxt-beastcss',
        'nuxt-vitalizer',
        'nuxt-booster',
        'nuxt-gtag',
        '@zadigetvoltaire/nuxt-gtm',
        // '@nuxtjs/turnstile',
        // 'nuxt-multi-cache',
        ['@nuxtjs/robots', { configPath: resolve('./robots.config') }],
        [
            'nuxt-cache-ssr',
            {
                enabled: true,
                store: {
                    type: 'memory',
                    max: 500,
                    ttl: 1000 * 60 * 15,
                },
                pages: ['/'],
                key: (route: string, headers: any, device: any) => {
                    if (headers.cookie?.includes('user=')) return false
                    if (device.isMobile) {
                        return `${route}:mb`
                    }
                    return `${route}:pc`
                }
            },
        ],
    ],

    // turnstile: {
    //     siteKey: process.env.TURNSTILE_SITE_KEY,
    //     addValidateEndpoint: true
    // },

    gtag: {
        id: process.env.GOOGLE_ANALYTICS_ID,
    },

    gtm: {
        id: process.env.GOOGLE_TAG_MANAGE_ID,
    },

    beastcss: {
        config: {
            minifyCss: true,
            preloadExternalStylesheets: true,
            asyncLoadExternalStylesheets: true,
            autoRemoveStyleTags: true,
            fontFace: true,
        },
    },

    booster: {
        detection: {
            performance: true,
            browserSupport: true,
        },

        performanceMetrics: {
            device: {
                hardwareConcurrency: { min: 2, max: 48 },
                deviceMemory: { min: 2 },
            },
            timing: {
                fcp: 800,
                dcl: 1200,
            },
        },

        optimizeSSR: {
            cleanPreloads: true,
            cleanPrefetches: true,
            inlineStyles: true,
        },
    },

    vitalizer: {
        disableStylesheets: 'entry',
        disablePrefetchLinks: true,
        disablePreloadLinks: false, // Keep preload for critical resources
    },

    robots: {
        configPath: resolve('./robots.config'),
        rules: {
            UserAgent: '*',
            Allow: '/',
            Disallow: '',
        },
    },

    site: {
        url: process.env.SITE_URL,
    },

    // Enhanced Image optimization for better delivery
    image: {
        quality: 80, // Reduced from 85 for better compression
        format: ['avif', 'webp', 'jpg'],
        screens: {
            xs: 320,
            sm: 640,
            md: 768,
            lg: 1024,
            xl: 1280,
            xxl: 1536,
        },
        densities: [1, 2],
        // Optimize for different use cases
        presets: {
            hero: {
                modifiers: {
                    format: 'avif,webp,jpg',
                    quality: 85, // Higher quality for hero images
                    fit: 'cover',
                }
            },
            thumbnail: {
                modifiers: {
                    format: 'avif,webp,jpg',
                    quality: 75, // Lower quality for thumbnails
                    fit: 'cover',
                }
            },
            icon: {
                modifiers: {
                    format: 'avif,webp,png',
                    quality: 90, // High quality for small icons
                    fit: 'contain',
                }
            },
            background: {
                modifiers: {
                    format: 'avif,webp,jpg',
                    quality: 70, // Lower quality for backgrounds
                    fit: 'cover',
                }
            }
        },
        // Enable static generation for better performance
        provider: 'ipx',
        ipx: {
            modifiers: {
                quality: 80,
                format: 'avif,webp,jpg'
            }
        },
        // Enable image optimization
        domains: [],
        alias: {},
        // Preload critical images
        preload: {
            // Preload hero images
            hero: true,
        }
    },

    sitemap,

    runtimeConfig: {
        basicAuth: process.env.NUXT_BASIC_AUTH,
        //   turnstile: {
        //       secretKey: process.env.TURNSTILE_SECRET_KEY,
        //   },
        recaptchaV3SiteKey: process.env.RECAPTCHA_V3_SITE_KEY || '',
        recaptchaV2SiteKey: process.env.RECAPTCHA_V2_SITE_KEY || '',
        public: {
            commingSoon: true,
            apiBaseUrl: '',
            apiVersion: '',
            staticUrl: process.env.STATIC_URL || '/assets/images',
            // staticUrl: '/assets/images',
            socketUrl: process.env.NUXT_PUBLIC_SOCKET_URL || '/',
            // to override use NUXT_PUBLIC_JACKPOT_INTERVAL
            jackpotInterval: process.env.JACKPOT_INTERVAL || 5000,
            jackpotApi: process.env.JACKPOT_API || '',
            // to override use NUXT_PUBLIC_JACKPOT_INTERVAL
            refreshInterval: 3000,
            BRAND_NAME: process.env.BRAND_NAME || 'SV88',
            ENABLE_QUICK_BET: process.env.ENABLE_QUICK_BET,
            SITE_URL: process.env.SITE_URL,
            API_URL: process.env.API_URL,
            API_PROMOTION: process.env.API_PROMOTION,
            NUXT_PUBLIC_SOCKET_URL: process.env.NUXT_PUBLIC_SOCKET_URL,
            CONTACT_FACEBOOK_LINK: process.env.CONTACT_FACEBOOK_LINK,
            CONTACT_MESSENGER_LINK: process.env.CONTACT_MESSENGER_LINK,
            CONTACT_TELEGRAM_LINK: process.env.CONTACT_TELEGRAM_LINK,
            PROMOTION_TELEGRAM_LINK: process.env.PROMOTION_TELEGRAM_LINK,
            CONTACT_EMAIL_LINK: process.env.CONTACT_EMAIL_LINK,
            GAME_URL: process.env.GAME_URL,
            LIVE_CHAT_ID: process.env.LIVE_CHAT_ID,
            KSPORT_DESKTOP_URL: process.env.KSPORT_DESKTOP_URL,
            KSPORT_MOBILE_URL: process.env.KSPORT_MOBILE_URL,
            QUAYSO5_URL: process.env.QUAYSO5_URL,
            NEWS_IMAGE_URL: process.env.NEWS_IMAGE_URL,
            HOME_NEWS_CATEGORY_ID: process.env.HOME_NEWS_CATEGORY_ID,
            HOST_NAME: process.env.HOST_NAME,
            GO88_URL: process.env.GO88_URL,
            REGISTER_BONUS:
                process.env.NUXT_PUBLIC_REGISTER_BONUS || '10.000.000',
            TELEGRAM_URL: process.env.TELEGRAM_URL,
            OLYMPIC_EVENT_START_DAY: process.env.OLYMPIC_EVENT_START_DAY,
            OLYMPIC_EVENT_END_DAY: process.env.OLYMPIC_EVENT_END_DAY,
            OLYMPIC_EVENT_ROUND2_START_DAY:
                process.env.OLYMPIC_EVENT_ROUND2_START_DAY,
            OLYMPIC_EVENT_ROUND1_END_DAY:
                process.env.OLYMPIC_EVENT_ROUND1_END_DAY,
            EURO_RANKING_EVENT_START_DAY:
                process.env.EURO_RANKING_EVENT_START_DAY,
            EURO_RANKING_EVENT_END_DAY: process.env.EURO_RANKING_EVENT_END_DAY,
            EURO_EVENT_START_DAY: process.env.EURO_EVENT_START_DAY,
            EURO_EVENT_END_DAY: process.env.EURO_EVENT_END_DAY,
            GET_CODE_TELEGRAM_LINK: process.env.GET_CODE_TELEGRAM_LINK,
            LINK_DOWNLOAD_WINDOWS: process.env.LINK_DOWNLOAD_WINDOWS,
            LINK_DOWNLOAD_MAC: process.env.LINK_DOWNLOAD_MAC,
            LINK_DOWNLOAD_IOS: process.env.LINK_DOWNLOAD_IOS,
            LINK_DOWNLOAD_ANDROID: process.env.LINK_DOWNLOAD_ANDROID,
            CODEPAY_NEW_QR_TIME: process.env.CODEPAY_NEW_QR_TIME,
            CODEPAY_SUCCESS_TIME: process.env.CODEPAY_SUCCESS_TIME,
            BANKING_MAINTENANCE_TIME: process.env.BANKING_MAINTENANCE_TIME,
            BINANCE_URL: process.env.BINANCE_URL,
            COIN12_URL: process.env.COIN12_URL,
            LINK_CREATE_ACCOUNT_COIN12: process.env.LINK_CREATE_ACCOUNT_COIN12,
            LINK_INSTRUCTION_SELL_COIN12:
                process.env.LINK_INSTRUCTION_SELL_COIN12,
            HUOBI_URL: process.env.HUOBI_URL,
            BRAND_EMAIL: process.env.BRAND_EMAIL,
            REMITANO_URL: process.env.REMITANO_URL,
            insuranceSportStartTime:
                process.env.INSURANCE_SPORT_EVENT_START_TIME,
            REFRESH_INTERVAL: process.env.REFRESH_INTERVAL,
            CONTACT_AGENCY_MAIL: process.env.CONTACT_AGENCY_MAIL,
            CONTACT_VIBER: process.env.CONTACT_VIBER,
            CONTACT_ZALO: process.env.CONTACT_ZALO,
            TOP_EVENT_START_DAY: process.env.TOP_EVENT_START_DAY,
            TOP_EVENT_END_DAY: process.env.TOP_EVENT_END_DAY,
            TOP_EVENT_ROUND_DURATION_DAYS:
                process.env.TOP_EVENT_ROUND_DURATION_DAYS,
            FREE_SPIN_EVENT_START_DAY: process.env.FREE_SPIN_EVENT_START_DAY,
            FREE_SPIN_EVENT_END_DAY: process.env.FREE_SPIN_EVENT_END_DAY,
            SVIP_EVENT_START_DAY: process.env.SVIP_EVENT_START_DAY,
            SVIP_EVENT_END_DAY: process.env.SVIP_EVENT_END_DAY,
            TET_EVENT_START_DAY: process.env.TET_EVENT_START_DAY,
            TET_EVENT_END_DAY: process.env.TET_EVENT_END_DAY,
            GOLDEN_STAR_WARRIORS_EVENT_START_DAY: process.env.GOLDEN_STAR_WARRIORS_EVENT_START_DAY,
            GOLDEN_STAR_WARRIORS_EVENT_END_DAY: process.env.GOLDEN_STAR_WARRIORS_EVENT_END_DAY,
            GOLDEN_STAR_WARRIORS_EVENT_ROUND_DURATION_DAYS: process.env.GOLDEN_STAR_WARRIORS_EVENT_ROUND_DURATION_DAYS,
            GEO_BLOCK_COUNTRIES: process.env.GEO_BLOCK_COUNTRIES,
            API_EVENT_RESKIN: process.env.API_EVENT_RESKIN,
            API_EVENT_RESKIN_WORLD_CUP: process.env.API_EVENT_RESKIN_WORLD_CUP,
            CLUB_WORLD_CUP_EVENT_START_DAY: process.env.CLUB_WORLD_CUP_EVENT_START_DAY,
            CLUB_WORLD_CUP_EVENT_END_DAY: process.env.CLUB_WORLD_CUP_EVENT_END_DAY,
            CLUB_WORLD_CUP_EVENT_ROUND_DURATION_DAYS: process.env.CLUB_WORLD_CUP_EVENT_ROUND_DURATION_DAYS,
            UEFA_MODAL_SHOW_AGAIN_HOURS: process.env.UEFA_MODAL_SHOW_AGAIN_HOURS || 48,
            UEFA_CHAMPION_LEAGUE_METHOD: process.env.UEFA_CHAMPION_LEAGUE_METHOD,
            UEFA_EVENT_START_DAY: process.env.UEFA_EVENT_START_DAY,
            UEFA_EVENT_END_DAY: process.env.UEFA_EVENT_END_DAY,
            recaptchaSiteKey: process.env.RECAPTCHA_SITE_KEY,
            LUCKY_DRAW_START_TIME: process.env.LUCKY_DRAW_START_TIME,
            LUCKY_DRAW_END_TIME: process.env.LUCKY_DRAW_END_TIME,
            recaptchaV3SiteKey: process.env.RECAPTCHA_V3_SITE_KEY,
            recaptchaV2SiteKey: process.env.RECAPTCHA_V2_SITE_KEY,
            FIFA_CLUB_MODAL_SHOW_AGAIN_HOURS: process.env.FIFA_CLUB_MODAL_SHOW_AGAIN_HOURS,
            FIFA_CLUB_METHOD: process.env.FIFA_CLUB_METHOD,
            FIFA_CLUB_EVENT_START_DAY: process.env.FIFA_CLUB_EVENT_START_DAY,
            FIFA_CLUB_EVENT_END_DAY: process.env.FIFA_CLUB_EVENT_END_DAY,
        },
    },

    routeRules: {
        // API proxy rules
        '/api-promotion/v1/**': {
            proxy: {
                to: `${process.env.API_PROMOTION}/api-promotion/v1/**`,
                headers: {
                    'Accept-Encoding': 'gzip, deflate, br, compress',
                },
            },
        },
        '/api/v1/**': {
            proxy: {
                to: `${process.env.API_URL}/api/v1/**`,
                headers: {
                    'Accept-Encoding': 'gzip, deflate, br, compress',
                },
            },
        },
        '/api/v2/**': {
            proxy: {
                to: `${process.env.API_URL}/api/v2/**`,
                headers: {
                    'Accept-Encoding': 'gzip, deflate, br, compress',
                },
            },
        },

        // Static assets cache rules
        '/assets/images/**': {
            headers: {
                'Cache-Control': 'public, max-age=31536000, immutable', // 1 year
                'Vary': 'Accept-Encoding',
            },
        },
        '/assets/js/**': {
            headers: {
                'Cache-Control': 'public, max-age=31536000, immutable', // 1 year
                'Vary': 'Accept-Encoding',
            },
        },
        '/assets/css/**': {
            headers: {
                'Cache-Control': 'public, max-age=31536000, immutable', // 1 year
                'Vary': 'Accept-Encoding',
            },
        },
        '/assets/fonts/**': {
            headers: {
                'Cache-Control': 'public, max-age=31536000, immutable', // 1 year
                'Vary': 'Accept-Encoding',
            },
        },

        // Favicon and manifest
        '/favicon.ico': {
            headers: {
                'Cache-Control': 'public, max-age=86400', // 1 day
            },
        },

        // Service worker
        '/sw.js': {
            headers: {
                'Cache-Control': 'public, max-age=0, must-revalidate', // No cache for SW
            },
        },

        // "/": { cache: { maxAge: 60 * 60, base: "redis" } },
    },

    build: {
        splitChunks: {
            layouts: true,
            pages: true,
            commons: true,
            maxSize: 25000,
        },
        optimization: {
            runtimeChunk: true,
            splitChunks: {
                cacheGroups: {
                    vendor: {
                        test: /[\\/]node_modules[\\/]/,
                        name: 'vendors',
                        chunks: 'all',
                        priority: 10,
                        maxSize: 40000
                    },
                    common: {
                        name: 'common',
                        minChunks: 2,
                        chunks: 'all',
                        priority: 5,
                        maxSize: 25000
                    }
                }
            }
        },
        extractCSS: {
            ignoreOrder: true,
        },
        sourcemap: isProduction ? false : true,
        terser: {
            terserOptions: {
                compress: {
                    drop_console: isProduction,
                    drop_debugger: isProduction,
                    pure_funcs: isProduction ? ['console.log', 'console.info', 'console.debug'] : [],
                    passes: 2,
                },
                mangle: {
                    safari10: false,
                },
                ecma: 2020,
            },
        },
    },

    i18n: {
        lazy: true,
        langDir: 'locales',
        strategy: 'no_prefix',
        defaultLocale: 'vi',
        fallbackLocale: 'en',
        detectBrowserLanguage: false,
        compilation: {
            strictMessage: false,
        },
        locales: [
            {
                code: 'vi',
                file: 'vi.json',
                text: 'Tiếng Việt',
                iso: 'vi-VI',
            },
            {
                code: 'en',
                file: 'en.json',
                text: 'English',
                iso: 'en-US',
            },
        ],
    },

    devServer: {
        port: process.env.PORT ?? 9000,
    },

    app: {
        head: {
            title: 'SV88',
            meta: [
                {
                    charset: 'utf8',
                },
                {
                    name: 'viewport',
                    content:
                        'width=device-width, initial-scale=1.0, maximum-scale=1.0',
                },
                { name: 'mobile-web-app-capable', content: 'yes' },
                {
                    hid: 'apple-mobile-web-app-status-bar-style',
                    name: 'apple-mobile-web-app-status-bar-style',
                    content: 'black-translucent',
                },
                { name: 'apple-mobile-web-app-title', content: 'Livescore' },
                {
                    hid: 'SV88',
                    content: 'SV88',
                },
            ],
            link: [
                { rel: 'preconnect', href: `https://fonts.googleapis.com` },
                {
                    href: `https://fonts.gstatic.com`,
                },
                { rel: 'preconnect', href: 'https://www.googletagmanager.com' },
                { rel: 'dns-prefetch', href: 'https://www.googletagmanager.com' },
                // Optimized font loading with font-display: swap
                {
                    rel: 'preconnect',
                    href: 'https://fonts.googleapis.com',
                },
                {
                    rel: 'preconnect',
                    href: 'https://fonts.gstatic.com',
                    crossorigin: 'anonymous',
                },
                {
                    rel: 'stylesheet',
                    href: 'https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap',
                },
                {
                    rel: 'manifest',
                    href: '/manifest.json',
                },
                {
                    rel: 'canonical',
                    href: `${process.env.SITE_URL}`,
                },
                {
                    rel: 'icon',
                    type: 'image/png',
                    sizes: '16x16',
                    href: '/assets/favicon/favicon.ico',
                },
                {
                    rel: 'preload',
                    as: 'image',
                    href: `${process.env.SITE_URL}/assets/images/home/<USER>/danh-de-mien-phi/pc.png`,
                },
                {
                    rel: 'preload',
                    as: 'image',
                    href: `${process.env.SITE_URL}/assets/images/home/<USER>/danh-de-mien-phi/pc.avif`,
                },
                {
                    rel: 'preload',
                    as: 'image',
                    href: `${process.env.SITE_URL}/assets/images/home/<USER>/danh-de-mien-phi/pc.webp`,
                },
                {
                    rel: 'preload',
                    as: 'image',
                    href: `${process.env.SITE_URL}/assets/images/home/<USER>/danh-de-mien-phi/mb.png`,
                },
                {
                    rel: 'preload',
                    as: 'image',
                    href: `${process.env.SITE_URL}/assets/images/home/<USER>/danh-de-mien-phi/mb.avif`,
                },
                {
                    rel: 'preload',
                    as: 'image',
                    href: `${process.env.SITE_URL}/assets/images/home/<USER>/danh-de-mien-phi/mb.webp`,
                },
                // Preload LCP images with proper media queries
                {
                    rel: 'preload',
                    as: 'image',
                    href: `${process.env.SITE_URL}/assets/images/home/<USER>/jackpot/pc.avif`,
                    type: 'image/avif',
                    media: '(min-width: 992px)',
                    fetchpriority: 'high',
                },
                {
                    rel: 'preload',
                    as: 'image',
                    href: `${process.env.SITE_URL}/assets/images/home/<USER>/jackpot/pc.webp`,
                    type: 'image/webp',
                    media: '(min-width: 992px)',
                    fetchpriority: 'high',
                },
                {
                    rel: 'preload',
                    as: 'image',
                    href: `${process.env.SITE_URL}/assets/images/home/<USER>/jackpot/pc.jpg`,
                    type: 'image/jpeg',
                    media: '(min-width: 992px)',
                    fetchpriority: 'low',
                },
                {
                    rel: 'preload',
                    as: 'image',
                    href: `${process.env.SITE_URL}/assets/images/home/<USER>/jackpot/mb.avif`,
                    type: 'image/avif',
                    media: '(max-width: 991px)',
                    fetchpriority: 'high',
                },
                {
                    rel: 'preload',
                    as: 'image',
                    href: `${process.env.SITE_URL}/assets/images/home/<USER>/jackpot/mb.webp`,
                    type: 'image/webp',
                    media: '(max-width: 991px)',
                    fetchpriority: 'high',
                },
                {
                    rel: 'preload',
                    as: 'image',
                    href: `${process.env.SITE_URL}/assets/images/home/<USER>/jackpot/mb.jpg`,
                    type: 'image/jpeg',
                    media: '(max-width: 991px)',
                    fetchpriority: 'low',
                },

            ],
            style: [
                {
                    innerHTML: `
                        .hero-banner{display:block;width:100%;aspect-ratio:1920/600;min-height:200px;position:relative;overflow:hidden}
                        @media (max-width:991px){.hero-banner{aspect-ratio:430/195;min-height:150px}}
                        .hero-banner img[loading="eager"]{content-visibility:auto}
                    `,
                    type: 'text/css'
                }
            ],
            // script: [
            //     {
            //         type: 'application/ld+json',
            //         innerHTML: JSON.stringify(jsonData),
            //     },
            // ],
        },
    },

            vite: {
                define: {},
                css: {
                    minify: true,
                    devSourcemap: !isProduction,
                    preprocessorOptions: {
                        scss: {
                            additionalData: `
                              @import "/assets/scss/variables/_variables.scss";
                              @import "/assets/scss/_function.scss";
                              @import "/assets/scss/_mixins.scss";
                          `,
                        },
                    },
                },
                optimizeDeps: {
                    include: [
                        'vue', 
                        'nuxt', 
                        'vue-router',
                        'dayjs',
                        'dayjs/plugin/utc',
                        'dayjs/plugin/timezone',
                        'dayjs/plugin/isBetween',
                        'swiper/vue',
                        'swiper/modules'
                    ],
                    exclude: ['@nuxt/ui']
                },
                build: {
                    minify: 'terser',
                    cssCodeSplit: true,
                    chunkSizeWarningLimit: 200,
                    sourcemap: isProduction ? false : true,
                    rollupOptions: {
                        output: {
                            // Separate CSS chunks for better caching
                            assetFileNames: (assetInfo: any) => {
                                if (assetInfo.name?.endsWith('.css')) {
                                    return '_nuxt/css/[name].[hash][extname]'
                                }
                                return '_nuxt/[name].[hash][extname]'
                            },
                            // Split vendor CSS and optimize for modern browsers
                            manualChunks: {
                                'vendor-css': ['swiper/css', 'nuxt-icon/css'],
                                // Separate third-party tracking scripts to reduce main bundle
                                'vendor-facebook': ['fbevents'],
                                'vendor-analytics': ['gtag']
                            }
                        }
                    },
                    terserOptions: {
                        compress: {
                            drop_console: process.env.NODE_ENV === 'production',
                            drop_debugger: true,
                            // Remove legacy polyfills and transforms
                            pure_funcs: ['console.log', 'console.info', 'console.debug'],
                            // Modern browser optimizations
                            ecma: 2020,
                            module: true,
                        },
                        mangle: {
                            safari10: false, // Don't support Safari 10 to reduce polyfills
                        },
                        output: {
                            comments: false,
                            ecma: 2020,
                        },
                    },
                },
                esbuild: {
                    drop: isProduction ? ['console', 'debugger'] : [],
                    pure: isProduction ? [
                        'console.log',
                        'console.error',
                        'console.warn',
                        'console.debug',
                        'console.trace',
                    ] : [],
                },
                plugins: [
                    viteCompression({ algorithm: 'gzip' }),
                    viteCompression({ algorithm: 'brotliCompress' }),
                ],
            },

    nitro: {
        minify: isProduction,
        sourceMap: !isProduction,
        prerender: {
            // routes: ['/', '/game-page'],
            ignore: ['/user'],
        },
        compressPublicAssets: true,
        routeRules: {
            // Nuxt generated assets (JS, CSS with hash)
            '/_nuxt/**': {
                headers: {
                    'Cache-Control': 'public, max-age=31536000, immutable',
                    'Vary': 'Accept-Encoding',
                },
            },
            // Static assets
            '/assets/**': {
                headers: {
                    'Cache-Control': 'public, max-age=31536000, immutable',
                    'Vary': 'Accept-Encoding',
                },
            },
            // Images with different cache based on type
            '/assets/images/home/<USER>/**': {
                headers: {
                    'Cache-Control': 'public, max-age=31536000, immutable',
                    'Vary': 'Accept-Encoding',
                },
            },
            '/assets/images/**': {
                headers: {
                    'Cache-Control': 'public, max-age=2592000', // 30 days for other images
                    'Vary': 'Accept-Encoding',
                },
            },
        },
        storage: {
            redis: {
                driver: 'redis',
                username: '',
                password: '',
                host: '127.0.0.1',
                port: 6379,
                lazyConnect: true,
                ttl: 60 * 5,
            },
        },
    },

    loading: false,
    ssr: true,

    serverMiddleware: [
        { path: '/', handler: '~/server/middleware/redirect.js' },
    ],

    devtools: {
        enabled: true,
    },

    imports: {
        autoImport: true,
    },

    env: {
        BUILDID,
    },

    ignore: ['pages/user/notification/**', 'pages/user/agency/**'],
}
