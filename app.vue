<template>
    <Html>
        <Body>
            <NuxtLayout :name="layout">
                <h1 v-if="h1SeoTitle" class="hidden">
                    {{ h1SeoTitle }}
                </h1>
                <!-- <NuxtLoadingIndicator :height="5" :duration="3000" :throttle="400" /> -->
                <NuxtPage />
            </NuxtLayout>
        </Body>
    </Html>
</template>
<script setup>
import { seoMeta, SEO_TITLE } from '~/resources/seo-meta'
import { pathPost } from '~/constants/page-urls.ts'
import { jsonData } from '~/constants/seo-schema-json'

const layout = 'default'

const { setLocale, locales, defaultLocale, t } = useI18n()
const brandName = useRuntimeConfig().public.BRAND_NAME
const route = useRoute()
const url = route.path || 'all'

const activelang = useCookie('lang', {
    default: () => 'vi',
    maxAge: 60 * 60 * 24 * 365,
})

const localeToset = locales.value.find((l) => l.code === activelang.value)

if (localeToset !== undefined) {
    await setLocale(localeToset.code)
} else {
    //update cookies and render in VI
    activelang.value = defaultLocale.value || 'vi'
    await setLocale(defaultLocale.value)
}

useHead({
    htmlAttrs: {
        lang: 'vi',
    },
    link: [
        {
            rel: 'canonical',
            href: `${useRuntimeConfig().public.SITE_URL}${route.fullPath}`,
            'data-n-head': 'ssr',
        },
    ],
    script: [
        {
            'data-n-head': 'ssr',
            type: 'application/ld+json',
            innerHTML: JSON.stringify(jsonData(url, t)),
        },
        route.name === 'livecasino-type'
            ? {
                  src: '/assets/js/nanoplayer.4.min.js',
                  type: 'text/javascript',
              }
            : {},
    ],
})

const h1SeoTitle = ref('')
const setSeo = (routerName) => {
    const meta = seoMeta(brandName, t, SEO_TITLE[routerName] || 'all')
    h1SeoTitle.value = meta?.h1
    useSeoMeta({
        ...meta,
    })
}
watch(
    () => [route.name, route.query.type],
    () => {
        const slug =
            route.params.type &&
            ['cong-game', 'cong-game-type'].includes(route.name)
                ? route.params.type
                : route.name
        pathPost.includes(slug) || setSeo(slug)
    },
    { immediate: true }
)

onMounted(() => {
    // Simple performance optimizations
    const optimizePerformance = () => {
        // Add passive to scroll listeners
        const scrollElements = document.querySelectorAll('[data-scroll]')
        scrollElements.forEach(element => {
            element.addEventListener('scroll', () => {}, { passive: true })
        })

        // Optimize images
        const images = document.querySelectorAll('img:not([loading])')
        images.forEach((img, index) => {
            if (index > 3) { // Lazy load images below the fold
                img.setAttribute('loading', 'lazy')
            }
        })
    }

    // Run optimizations after a short delay
    setTimeout(optimizePerformance, 100)
})
</script>
