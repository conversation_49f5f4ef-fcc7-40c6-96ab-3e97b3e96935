const lighthouse = require('lighthouse')
const chromeLauncher = require('chrome-launcher')
const fs = require('fs')
const path = require('path')

// Configuration
const SITE_URL = process.env.SITE_URL || 'http://localhost:3000'
const OUTPUT_DIR = './lighthouse-reports'

// Lighthouse configurations
const configs = {
    desktop: {
        extends: 'lighthouse:default',
        settings: {
            formFactor: 'desktop',
            throttling: {
                rttMs: 40,
                throughputKbps: 10240,
                cpuSlowdownMultiplier: 1,
                requestLatencyMs: 0,
                downloadThroughputKbps: 0,
                uploadThroughputKbps: 0
            },
            screenEmulation: {
                mobile: false,
                width: 1920,
                height: 1080,
                deviceScaleFactor: 1,
                disabled: false
            },
            emulatedUserAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
    },
    mobile: {
        extends: 'lighthouse:default',
        settings: {
            formFactor: 'mobile',
            throttling: {
                rttMs: 150,
                throughputKbps: 1638.4,
                cpuSlowdownMultiplier: 4,
                requestLatencyMs: 150,
                downloadThroughputKbps: 1638.4,
                uploadThroughputKbps: 750
            },
            screenEmulation: {
                mobile: true,
                width: 375,
                height: 667,
                deviceScaleFactor: 2,
                disabled: false
            }
        }
    }
}

async function runLighthouse(url, config, outputName) {
    console.log(`🚀 Running Lighthouse for ${outputName}...`)
    
    const chrome = await chromeLauncher.launch({
        chromeFlags: ['--headless', '--no-sandbox', '--disable-dev-shm-usage']
    })
    
    try {
        const runnerResult = await lighthouse(url, {
            port: chrome.port,
            output: ['html', 'json'],
            logLevel: 'info'
        }, config)

        // Save HTML report
        const htmlReport = runnerResult.report[0]
        const htmlPath = path.join(OUTPUT_DIR, `${outputName}.html`)
        fs.writeFileSync(htmlPath, htmlReport)

        // Save JSON report
        const jsonReport = runnerResult.report[1]
        const jsonPath = path.join(OUTPUT_DIR, `${outputName}.json`)
        fs.writeFileSync(jsonPath, jsonReport)

        // Extract key metrics
        const lhr = runnerResult.lhr
        const metrics = {
            performance: Math.round(lhr.categories.performance.score * 100),
            accessibility: Math.round(lhr.categories.accessibility.score * 100),
            bestPractices: Math.round(lhr.categories['best-practices'].score * 100),
            seo: Math.round(lhr.categories.seo.score * 100),
            lcp: lhr.audits['largest-contentful-paint'].numericValue,
            fid: lhr.audits['max-potential-fid'].numericValue,
            cls: lhr.audits['cumulative-layout-shift'].numericValue,
            fcp: lhr.audits['first-contentful-paint'].numericValue,
            ttfb: lhr.audits['server-response-time'].numericValue
        }

        console.log(`✅ ${outputName} Results:`)
        console.log(`   Performance: ${metrics.performance}/100`)
        console.log(`   Accessibility: ${metrics.accessibility}/100`)
        console.log(`   Best Practices: ${metrics.bestPractices}/100`)
        console.log(`   SEO: ${metrics.seo}/100`)
        console.log(`   LCP: ${Math.round(metrics.lcp)}ms`)
        console.log(`   FID: ${Math.round(metrics.fid)}ms`)
        console.log(`   CLS: ${metrics.cls.toFixed(3)}`)
        console.log(`   FCP: ${Math.round(metrics.fcp)}ms`)
        console.log(`   TTFB: ${Math.round(metrics.ttfb)}ms`)
        console.log(`   Reports saved to: ${htmlPath}`)

        return metrics
    } finally {
        await chrome.kill()
    }
}

async function runAllTests() {
    console.log('🔍 Starting Lighthouse Performance Tests...')
    console.log(`Testing URL: ${SITE_URL}`)
    
    // Ensure output directory exists
    if (!fs.existsSync(OUTPUT_DIR)) {
        fs.mkdirSync(OUTPUT_DIR, { recursive: true })
    }

    const results = {}

    try {
        // Run desktop test
        results.desktop = await runLighthouse(SITE_URL, configs.desktop, 'desktop')
        
        // Run mobile test
        results.mobile = await runLighthouse(SITE_URL, configs.mobile, 'mobile')

        // Generate comparison report
        const comparison = {
            timestamp: new Date().toISOString(),
            url: SITE_URL,
            results,
            recommendations: generateRecommendations(results)
        }

        const comparisonPath = path.join(OUTPUT_DIR, 'comparison.json')
        fs.writeFileSync(comparisonPath, JSON.stringify(comparison, null, 2))

        console.log('\n📊 Performance Comparison:')
        console.log('Desktop vs Mobile:')
        console.log(`Performance: ${results.desktop.performance} vs ${results.mobile.performance}`)
        console.log(`LCP: ${Math.round(results.desktop.lcp)}ms vs ${Math.round(results.mobile.lcp)}ms`)
        console.log(`FCP: ${Math.round(results.desktop.fcp)}ms vs ${Math.round(results.mobile.fcp)}ms`)
        
        console.log('\n🎯 Recommendations:')
        comparison.recommendations.forEach(rec => console.log(`   • ${rec}`))

    } catch (error) {
        console.error('❌ Error running Lighthouse tests:', error)
        process.exit(1)
    }
}

function generateRecommendations(results) {
    const recommendations = []
    
    // Performance recommendations
    if (results.mobile.performance < 90) {
        recommendations.push('Optimize for mobile performance - consider image compression and code splitting')
    }
    
    if (results.desktop.lcp > 2500 || results.mobile.lcp > 2500) {
        recommendations.push('Improve LCP by optimizing hero images with AVIF format and preloading')
    }
    
    if (results.desktop.cls > 0.1 || results.mobile.cls > 0.1) {
        recommendations.push('Reduce CLS by adding width/height attributes to images')
    }
    
    if (results.desktop.fcp > 1800 || results.mobile.fcp > 1800) {
        recommendations.push('Improve FCP by inlining critical CSS and deferring non-critical resources')
    }

    return recommendations
}

// Run tests if called directly
if (require.main === module) {
    runAllTests().catch(console.error)
}

module.exports = { runAllTests, runLighthouse }
